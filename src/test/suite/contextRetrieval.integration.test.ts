import * as assert from 'assert';
import * as vscode from 'vscode';
import * as nock from 'nock';
import { ContextManager } from '../../services/contextManager';
import { ContextRetrievalTool } from '../../tools/contextRetrievalTool';
import { Logger } from '../../services/logger';
import { StorageService } from '../../services/storage';
import { GitHubApiService } from '../../api/github';
import { HttpClient } from '../../services/httpClient';
import { RateLimitManager } from '../../services/rateLimitManager';
import { 
  mockRepository, 
  mockGithubContent, 
  mockMainMdcContent,
  mockFileContentResponses 
} from '../fixtures/mockData';

suite('Context Retrieval Integration Tests', () => {
  let contextManager: ContextManager;
  let contextRetrievalTool: ContextRetrievalTool;
  let logger: Logger;
  let storageService: StorageService;
  let githubService: GitHubApiService;

  setup(async () => {
    // Create real services for integration testing
    logger = new Logger('Test', false);
    
    // Mock VS Code context for storage service
    const mockContext = {
      globalState: {
        get: () => undefined,
        update: () => Promise.resolve(),
        keys: () => []
      },
      workspaceState: {
        get: () => undefined,
        update: () => Promise.resolve(),
        keys: () => []
      }
    } as any;
    
    storageService = new StorageService(mockContext);
    
    const httpClient = new HttpClient(logger);
    const rateLimitManager = new RateLimitManager(logger);
    githubService = new GitHubApiService(httpClient, rateLimitManager, logger);
    
    contextManager = new ContextManager(logger, githubService, storageService);
    contextRetrievalTool = new ContextRetrievalTool(logger, contextManager);

    // Set up the test repository
    storageService.setLastRepository(mockRepository);
  });

  teardown(() => {
    nock.cleanAll();
  });

  test('End-to-end context retrieval with mocked GitHub API', async () => {
    // Mock GitHub API responses
    const baseUrl = 'https://api.github.com';
    
    // Mock main.mdc fetch
    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/main.mdc')
      .reply(200, [{
        name: 'main.mdc',
        path: 'main.mdc',
        download_url: `${baseUrl}/repos/test-org/test-repo/contents/main.mdc?ref=main`,
        type: 'file',
        sha: 'main123',
        size: 1024,
        url: `${baseUrl}/repos/test-org/test-repo/contents/main.mdc`,
        html_url: 'https://github.com/test-org/test-repo/blob/main/main.mdc',
        git_url: `${baseUrl}/repos/test-org/test-repo/git/blobs/main123`
      }]);

    // Mock main.mdc content
    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/main.mdc?ref=main')
      .reply(200, mockMainMdcContent);

    // Mock root directory listing for auto-discovery
    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/')
      .reply(200, [
        {
          name: 'backend',
          path: 'backend',
          type: 'dir',
          sha: 'backend123',
          size: 0,
          url: `${baseUrl}/repos/test-org/test-repo/contents/backend`,
          html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
          git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/backend123`
        },
        {
          name: 'frontend',
          path: 'frontend',
          type: 'dir',
          sha: 'frontend123',
          size: 0,
          url: `${baseUrl}/repos/test-org/test-repo/contents/frontend`,
          html_url: 'https://github.com/test-org/test-repo/tree/main/frontend',
          git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/frontend123`
        }
      ]);

    // Mock service directory listings
    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/backend')
      .reply(200, mockGithubContent.filter(item => item.path.startsWith('backend/')));

    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/frontend')
      .reply(200, mockGithubContent.filter(item => item.path.startsWith('frontend/')));

    // Mock file content downloads
    for (const [path, content] of Object.entries(mockFileContentResponses)) {
      if (path !== 'main.mdc') {
        nock(baseUrl)
          .get(`/repos/test-org/test-repo/contents/${path}`)
          .reply(200, [{
            name: path.split('/').pop(),
            path: path,
            download_url: `${baseUrl}/repos/test-org/test-repo/contents/${path}?ref=main`,
            type: 'file',
            sha: 'file123',
            size: content.length,
            url: `${baseUrl}/repos/test-org/test-repo/contents/${path}`,
            html_url: `https://github.com/test-org/test-repo/blob/main/${path}`,
            git_url: `${baseUrl}/repos/test-org/test-repo/git/blobs/file123`
          }]);

        nock(baseUrl)
          .get(`/repos/test-org/test-repo/contents/${path}?ref=main`)
          .reply(200, content);
      }
    }

    // Test the context retrieval tool
    const mockOptions = {
      input: {
        query: 'architecture patterns',
        maxRules: 5,
        includeContent: true
      }
    } as vscode.LanguageModelToolInvocationOptions<any>;

    const mockToken = {} as vscode.CancellationToken;

    const result = await contextRetrievalTool.invoke(mockOptions, mockToken);

    // Verify the result
    assert.ok(result instanceof vscode.LanguageModelToolResult);
    
    const parts = result.content;
    assert.ok(parts.length > 0);
    
    const firstPart = parts[0];
    if (firstPart instanceof vscode.LanguageModelTextPart) {
      // Should find at least one relevant rule
      assert.ok(firstPart.value.includes('relevant rules') || firstPart.value.includes('No relevant rules'));
      
      // If rules were found, verify the format
      if (firstPart.value.includes('relevant rules')) {
        assert.ok(firstPart.value.includes('**Rule'));
        assert.ok(firstPart.value.includes('Category:'));
        assert.ok(firstPart.value.includes('Service:'));
      }
    }
  });

  test('Context retrieval with no repository selected', async () => {
    // Clear the repository
    storageService.setLastRepository(null);

    const mockOptions = {
      input: {
        query: 'architecture patterns',
        maxRules: 5
      }
    } as vscode.LanguageModelToolInvocationOptions<any>;

    const mockToken = {} as vscode.CancellationToken;

    const result = await contextRetrievalTool.invoke(mockOptions, mockToken);

    const parts = result.content;
    const firstPart = parts[0];
    if (firstPart instanceof vscode.LanguageModelTextPart) {
      assert.ok(firstPart.value.includes('Error') || firstPart.value.includes('No repository'));
    }
  });

  test('Context retrieval with GitHub API errors', async () => {
    // Set up repository
    storageService.setLastRepository(mockRepository);

    // Mock GitHub API to return errors
    nock('https://api.github.com')
      .get('/repos/test-org/test-repo/contents/main.mdc')
      .reply(404, { message: 'Not Found' });

    nock('https://api.github.com')
      .get('/repos/test-org/test-repo/contents/')
      .reply(403, { message: 'API rate limit exceeded' });

    const mockOptions = {
      input: {
        query: 'architecture patterns',
        maxRules: 5
      }
    } as vscode.LanguageModelToolInvocationOptions<any>;

    const mockToken = {} as vscode.CancellationToken;

    const result = await contextRetrievalTool.invoke(mockOptions, mockToken);

    const parts = result.content;
    const firstPart = parts[0];
    if (firstPart instanceof vscode.LanguageModelTextPart) {
      // Should handle the error gracefully
      assert.ok(firstPart.value.includes('Error') || firstPart.value.includes('No relevant rules'));
    }
  });

  test('Context manager caching behavior', async () => {
    // Set up repository
    storageService.setLastRepository(mockRepository);

    // Mock successful GitHub API responses
    const baseUrl = 'https://api.github.com';
    
    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/main.mdc')
      .reply(404); // main.mdc not found, should trigger auto-discovery

    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/')
      .reply(200, [
        {
          name: 'backend',
          path: 'backend',
          type: 'dir',
          sha: 'backend123',
          size: 0,
          url: `${baseUrl}/repos/test-org/test-repo/contents/backend`,
          html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
          git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/backend123`
        }
      ]);

    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/backend')
      .reply(200, []);

    // First call should fetch from API
    const structure1 = await contextManager.getRepositoryStructure();
    assert.ok(structure1);

    // Second call should use cache (no additional API calls expected)
    const structure2 = await contextManager.getRepositoryStructure();
    assert.strictEqual(structure1, structure2);

    // Clear cache and verify it refetches
    contextManager.clearCache();
    
    // Set up the same mock again for the refetch
    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/')
      .reply(200, [
        {
          name: 'backend',
          path: 'backend',
          type: 'dir',
          sha: 'backend123',
          size: 0,
          url: `${baseUrl}/repos/test-org/test-repo/contents/backend`,
          html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
          git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/backend123`
        }
      ]);

    nock(baseUrl)
      .get('/repos/test-org/test-repo/contents/backend')
      .reply(200, []);

    const structure3 = await contextManager.getRepositoryStructure();
    assert.ok(structure3);
    assert.notStrictEqual(structure1, structure3); // Should be a new instance
  });
});
