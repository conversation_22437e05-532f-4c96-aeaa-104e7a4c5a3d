# Xendit-Copilot Testing Guide

This directory contains comprehensive tests for the xendit-copilot VS Code extension.

## Test Structure

```
src/test/
├── fixtures/           # Mock data and test fixtures
│   └── mockData.ts    # Shared mock data for tests
├── unit/              # Unit tests
│   ├── services/      # Service layer tests
│   └── tools/         # Tool layer tests
├── suite/             # Integration tests
│   ├── index.ts       # Test runner configuration
│   ├── extension.test.ts                    # Extension activation tests
│   └── contextRetrieval.integration.test.ts # End-to-end tests
├── tsconfig.json      # TypeScript config for tests
├── runTest.ts         # VS Code test runner
└── README.md          # This file
```

## Running Tests

### Prerequisites

1. Install dependencies:
   ```bash
   npm install
   ```

2. Compile the extension and tests:
   ```bash
   npm run pretest
   ```

### Test Commands

- **Run all tests**: `npm test`
- **Run unit tests only**: `npm run test:unit`
- **Run integration tests only**: `npm run test:integration`
- **Run with coverage**: `npm run test:coverage`

### Individual Test Suites

You can also run specific test files using Mocha directly:

```bash
# Unit tests
npx mocha out/test/unit/services/ruleParser.test.js
npx mocha out/test/unit/services/repositoryParser.test.js
npx mocha out/test/unit/services/contextManager.test.js
npx mocha out/test/unit/tools/contextRetrievalTool.test.js

# Integration tests
node out/test/runTest.js
```

## Test Categories

### Unit Tests

Unit tests focus on individual components in isolation:

- **RuleParser Tests** (`unit/services/ruleParser.test.ts`):
  - Rule filename parsing validation
  - Content parsing and summary extraction
  - Error handling for invalid formats

- **RepositoryParser Tests** (`unit/services/repositoryParser.test.ts`):
  - main.mdc parsing
  - Rule file discovery
  - Repository structure validation
  - Auto-discovery when main.mdc is missing

- **ContextManager Tests** (`unit/services/contextManager.test.ts`):
  - Context retrieval workflow
  - Caching behavior
  - Session management
  - Rule relevance scoring

- **ContextRetrievalTool Tests** (`unit/tools/contextRetrievalTool.test.ts`):
  - Parameter validation
  - Response formatting
  - Error handling
  - VS Code integration

### Integration Tests

Integration tests verify end-to-end functionality:

- **Extension Tests** (`suite/extension.test.ts`):
  - Extension activation
  - Command registration
  - Chat tool registration
  - Configuration availability

- **Context Retrieval Integration** (`suite/contextRetrieval.integration.test.ts`):
  - Complete workflow with mocked GitHub API
  - Error scenarios
  - Caching behavior
  - Real service integration

## Test Data and Mocking

### Mock Data (`fixtures/mockData.ts`)

Contains realistic test data including:
- Sample GitHub repositories
- Rule metadata and content
- Repository structures
- API responses

### Mocking Strategy

- **Unit Tests**: Use Sinon.js for mocking dependencies
- **Integration Tests**: Use nock for HTTP mocking
- **VS Code APIs**: Mock VS Code extension APIs where needed

## Coverage Requirements

The test suite maintains high code coverage standards:

- **Lines**: 80% minimum
- **Statements**: 80% minimum  
- **Functions**: 80% minimum
- **Branches**: 70% minimum

Coverage reports are generated in the `coverage/` directory.

## Writing New Tests

### Unit Test Template

```typescript
import * as assert from 'assert';
import * as sinon from 'sinon';
import { YourClass } from '../../../path/to/YourClass';

suite('YourClass Unit Tests', () => {
  let yourClass: YourClass;
  let mockDependency: sinon.SinonStubbedInstance<DependencyType>;

  setup(() => {
    mockDependency = {
      method: sinon.stub()
    } as any;
    yourClass = new YourClass(mockDependency);
  });

  teardown(() => {
    sinon.restore();
  });

  test('should do something', () => {
    // Arrange
    mockDependency.method.returns('expected');
    
    // Act
    const result = yourClass.doSomething();
    
    // Assert
    assert.strictEqual(result, 'expected');
  });
});
```

### Integration Test Template

```typescript
import * as assert from 'assert';
import * as vscode from 'vscode';
import * as nock from 'nock';

suite('Integration Test Suite', () => {
  setup(() => {
    // Setup real services
  });

  teardown(() => {
    nock.cleanAll();
  });

  test('should work end-to-end', async () => {
    // Mock external APIs
    nock('https://api.github.com')
      .get('/some/endpoint')
      .reply(200, { data: 'test' });

    // Test the integration
    const result = await someIntegrationFunction();
    
    assert.ok(result);
  });
});
```

## Debugging Tests

### VS Code Debugging

1. Open the test file in VS Code
2. Set breakpoints
3. Run "Extension Tests" debug configuration
4. Debug through the test execution

### Console Debugging

Add console.log statements or use the VS Code Output panel to debug test execution.

## Continuous Integration

Tests are designed to run in CI environments:

- No external dependencies (all APIs mocked)
- Deterministic test data
- Proper cleanup between tests
- Clear error messages

## Best Practices

1. **Isolation**: Each test should be independent
2. **Descriptive Names**: Test names should clearly describe what is being tested
3. **Arrange-Act-Assert**: Follow the AAA pattern
4. **Mock External Dependencies**: Don't rely on real APIs or file systems
5. **Test Edge Cases**: Include error scenarios and boundary conditions
6. **Keep Tests Fast**: Unit tests should run quickly
7. **Maintain Test Data**: Keep mock data realistic and up-to-date
