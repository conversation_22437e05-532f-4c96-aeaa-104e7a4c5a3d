import * as assert from 'assert';
import * as sinon from 'sinon';
import { ContextManager } from '../../../services/contextManager';
import { RuleCategory } from '../../../types/rules';
import type { ILogger } from '../../../services/logger';
import type { IGitHubApiService } from '../../../api/github';
import type { IStorageService } from '../../../services/storage';
import { 
  mockRepository, 
  mockRepositoryStructure, 
  mockRuleContent, 
  mockContextRetrievalParams,
  mockFileContentResponses 
} from '../../fixtures/mockData';

suite('ContextManager Unit Tests', () => {
  let contextManager: ContextManager;
  let mockLogger: sinon.SinonStubbedInstance<ILogger>;
  let mockGithubService: sinon.SinonStubbedInstance<IGitHubApiService>;
  let mockStorageService: sinon.SinonStubbedInstance<IStorageService>;

  setup(() => {
    mockLogger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub()
    } as any;

    mockGithubService = {
      fetchRepositoryContent: sinon.stub(),
      fetchRepositoryContentRecursive: sinon.stub(),
      fetchFileContent: sinon.stub()
    } as any;

    mockStorageService = {
      getLastRepository: sinon.stub(),
      setLastRepository: sinon.stub(),
      getSelectedFiles: sinon.stub(),
      setSelectedFiles: sinon.stub(),
      clearSelectedFiles: sinon.stub()
    } as any;

    contextManager = new ContextManager(mockLogger, mockGithubService, mockStorageService);
  });

  teardown(() => {
    sinon.restore();
  });

  suite('retrieveContext', () => {
    test('should retrieve context successfully', async () => {
      // Setup mocks
      mockStorageService.getLastRepository.returns(mockRepository);
      
      // Mock repository structure retrieval
      sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockRepositoryStructure);
      
      // Mock rule search
      sinon.stub(contextManager, 'searchRules').resolves(mockRuleContent);
      
      // Mock rule content retrieval
      sinon.stub(contextManager, 'getRuleContent').resolves(mockRuleContent[0]);

      const result = await contextManager.retrieveContext(mockContextRetrievalParams);

      assert.strictEqual(result.query, mockContextRetrievalParams.query);
      assert.strictEqual(result.totalFound, mockRuleContent.length);
      assert.ok(result.rules.length > 0);
      assert.ok(result.scores.length > 0);
      assert.strictEqual(result.repositoryStructure, mockRepositoryStructure);
    });

    test('should handle no repository selected', async () => {
      mockStorageService.getLastRepository.returns(null);

      try {
        await contextManager.retrieveContext(mockContextRetrievalParams);
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert.ok(error instanceof Error);
        assert.ok(error.message.includes('No repository structure available'));
      }
    });

    test('should limit results based on maxRules parameter', async () => {
      mockStorageService.getLastRepository.returns(mockRepository);
      sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockRepositoryStructure);
      sinon.stub(contextManager, 'searchRules').resolves(mockRuleContent);
      sinon.stub(contextManager, 'getRuleContent').resolves(mockRuleContent[0]);

      const params = { ...mockContextRetrievalParams, maxRules: 1 };
      const result = await contextManager.retrieveContext(params);

      assert.strictEqual(result.rules.length, 1);
      assert.strictEqual(result.scores.length, 1);
    });
  });

  suite('getRepositoryStructure', () => {
    test('should return cached repository structure if valid', async () => {
      mockStorageService.getLastRepository.returns(mockRepository);
      
      // Set up cache with recent timestamp
      const recentStructure = { 
        ...mockRepositoryStructure, 
        lastParsed: new Date(Date.now() - 1000) // 1 second ago
      };
      (contextManager as any).repositoryStructureCache.set('test-org/test-repo', recentStructure);

      const result = await contextManager.getRepositoryStructure();

      assert.strictEqual(result, recentStructure);
    });

    test('should fetch new structure if cache is expired', async () => {
      mockStorageService.getLastRepository.returns(mockRepository);
      
      // Set up cache with old timestamp
      const oldStructure = { 
        ...mockRepositoryStructure, 
        lastParsed: new Date(Date.now() - 4000000) // More than 1 hour ago
      };
      (contextManager as any).repositoryStructureCache.set('test-org/test-repo', oldStructure);

      // Mock repository parser
      const mockRepositoryParser = (contextManager as any).repositoryParser;
      sinon.stub(mockRepositoryParser, 'parseRepositoryStructure').resolves(mockRepositoryStructure);

      const result = await contextManager.getRepositoryStructure();

      assert.notStrictEqual(result, oldStructure);
      assert.strictEqual(result, mockRepositoryStructure);
    });
  });

  suite('searchRules', () => {
    test('should search rules across all services', async () => {
      sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockRepositoryStructure);
      mockStorageService.getLastRepository.returns(mockRepository);

      const mockRepositoryParser = (contextManager as any).repositoryParser;
      sinon.stub(mockRepositoryParser, 'discoverRuleFiles').resolves([
        {
          filename: '00-clean-architecture.mdc',
          name: 'clean-architecture',
          category: RuleCategory.ARCHITECTURE,
          service: 'backend',
          path: 'backend/00-clean-architecture.mdc'
        }
      ]);

      sinon.stub(contextManager, 'getRuleContent').resolves(mockRuleContent[0]);

      const criteria = {
        keywords: ['architecture'],
        searchContent: true
      };

      const result = await contextManager.searchRules(criteria);

      assert.ok(result.length > 0);
      assert.strictEqual(result[0].name, 'clean-architecture');
    });

    test('should filter by service if specified', async () => {
      sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockRepositoryStructure);
      mockStorageService.getLastRepository.returns(mockRepository);

      const mockRepositoryParser = (contextManager as any).repositoryParser;
      sinon.stub(mockRepositoryParser, 'discoverRuleFiles').resolves([]);

      const criteria = {
        keywords: ['test'],
        service: 'backend',
        searchContent: true
      };

      await contextManager.searchRules(criteria);

      // Verify that discoverRuleFiles was called only for backend service
      assert.ok(mockRepositoryParser.discoverRuleFiles.calledWith(
        mockRepository.owner,
        mockRepository.name,
        'backend'
      ));
    });
  });

  suite('getRuleContent', () => {
    test('should return cached rule content if available', async () => {
      const metadata = mockRuleContent[0];
      const cacheKey = `${metadata.service}/${metadata.filename}`;
      
      // Set up cache
      (contextManager as any).ruleContentCache.set(cacheKey, metadata);

      const result = await contextManager.getRuleContent(metadata);

      assert.strictEqual(result, metadata);
    });

    test('should fetch and cache rule content if not cached', async () => {
      const metadata = {
        filename: '00-clean-architecture.mdc',
        name: 'clean-architecture',
        category: RuleCategory.ARCHITECTURE,
        service: 'backend',
        path: 'backend/00-clean-architecture.mdc'
      };

      mockStorageService.getLastRepository.returns(mockRepository);
      
      // Mock GitHub API calls
      mockGithubService.fetchRepositoryContent.resolves({
        success: true,
        data: [{
          name: '00-clean-architecture.mdc',
          path: 'backend/00-clean-architecture.mdc',
          download_url: 'https://example.com/download',
          type: 'file',
          sha: 'abc123',
          size: 1024,
          url: 'https://example.com/api',
          html_url: 'https://example.com/html',
          git_url: 'https://example.com/git'
        }]
      });

      mockGithubService.fetchFileContent.resolves({
        success: true,
        data: mockFileContentResponses['backend/00-clean-architecture.mdc']
      });

      // Mock rule parser
      const mockRuleParser = (contextManager as any).ruleParser;
      sinon.stub(mockRuleParser, 'parseRuleContent').returns(mockRuleContent[0]);

      const result = await contextManager.getRuleContent(metadata);

      assert.strictEqual(result.filename, metadata.filename);
      assert.strictEqual(result.name, metadata.name);
    });
  });

  suite('clearCache', () => {
    test('should clear all caches', () => {
      // Set up some cache data
      (contextManager as any).repositoryStructureCache.set('test', {});
      (contextManager as any).ruleContentCache.set('test', {});

      contextManager.clearCache();

      assert.strictEqual((contextManager as any).repositoryStructureCache.size, 0);
      assert.strictEqual((contextManager as any).ruleContentCache.size, 0);
    });
  });

  suite('session management', () => {
    test('should initialize new session', () => {
      const sessionId = contextManager.initializeSession();

      assert.ok(sessionId);
      assert.ok(sessionId.startsWith('session_'));

      const session = contextManager.getSession(sessionId);
      assert.ok(session);
      assert.strictEqual(session.id, sessionId);
      assert.ok(session.startTime instanceof Date);
      assert.ok(session.lastActivity instanceof Date);
    });

    test('should return null for non-existent session', () => {
      const session = contextManager.getSession('non-existent');
      assert.strictEqual(session, null);
    });
  });
});
