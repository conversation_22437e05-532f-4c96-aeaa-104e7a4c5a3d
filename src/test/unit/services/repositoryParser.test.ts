import * as assert from 'assert';
import * as sinon from 'sinon';
import { RepositoryParser } from '../../../services/repositoryParser';
import { RuleParser } from '../../../services/ruleParser';
import { RuleCategory } from '../../../types/rules';
import type { ILogger } from '../../../services/logger';
import type { IGitHubApiService } from '../../../api/github';
import { mockGithubContent, mockMainMdcContent, mockFileContentResponses } from '../../fixtures/mockData';

suite('RepositoryParser Unit Tests', () => {
  let repositoryParser: RepositoryParser;
  let mockLogger: sinon.SinonStubbedInstance<ILogger>;
  let mockGithubService: sinon.SinonStubbedInstance<IGitHubApiService>;
  let mockRuleParser: sinon.SinonStubbedInstance<RuleParser>;

  setup(() => {
    mockLogger = {
      debug: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub()
    } as any;

    mockGithubService = {
      fetchRepositoryContent: sinon.stub(),
      fetchRepositoryContentRecursive: sinon.stub(),
      fetchFileContent: sinon.stub()
    } as any;

    mockRuleParser = {
      parseRuleFilename: sinon.stub(),
      parseRuleContent: sinon.stub()
    } as any;

    repositoryParser = new RepositoryParser(mockLogger, mockGithubService, mockRuleParser as any);
  });

  teardown(() => {
    sinon.restore();
  });

  suite('parseMainMdc', () => {
    test('should parse main.mdc content correctly', () => {
      const result = repositoryParser.parseMainMdc(mockMainMdcContent);

      assert.strictEqual(result.description, 'This is a test repository for xendit-copilot testing.');
      assert.ok(result.services);
      assert.ok(result.services['backend-service']);
      assert.ok(result.services['frontend-service']);
      assert.strictEqual(result.totalRules, 0);
      assert.ok(result.lastParsed instanceof Date);
    });

    test('should handle empty main.mdc content', () => {
      const result = repositoryParser.parseMainMdc('');

      assert.strictEqual(result.description, undefined);
      assert.deepStrictEqual(result.services, {});
      assert.strictEqual(result.totalRules, 0);
    });

    test('should extract service names from headers', () => {
      const content = `# Repository

Description here.

## Backend Service
Backend rules.

## Frontend Service  
Frontend rules.

## Database Service
Database rules.`;

      const result = repositoryParser.parseMainMdc(content);

      assert.ok(result.services['backend-service']);
      assert.ok(result.services['frontend-service']);
      assert.ok(result.services['database-service']);
      assert.strictEqual(Object.keys(result.services).length, 3);
    });
  });

  suite('discoverRuleFiles', () => {
    test('should discover rule files in service directory', async () => {
      // Mock GitHub API response
      mockGithubService.fetchRepositoryContent.resolves({
        success: true,
        data: mockGithubContent
      });

      // Mock rule parser responses
      mockRuleParser.parseRuleFilename
        .withArgs('00-clean-architecture.mdc', 'backend', 'backend/00-clean-architecture.mdc')
        .returns({
          filename: '00-clean-architecture.mdc',
          name: 'clean-architecture',
          category: RuleCategory.ARCHITECTURE,
          service: 'backend',
          path: 'backend/00-clean-architecture.mdc'
        });

      mockRuleParser.parseRuleFilename
        .withArgs('<EMAIL>', 'backend', 'backend/<EMAIL>')
        .returns({
          filename: '<EMAIL>',
          name: 'pr-workflow',
          category: RuleCategory.WORKFLOWS,
          service: 'backend',
          path: 'backend/<EMAIL>',
          version: '1.2.0'
        });

      const result = await repositoryParser.discoverRuleFiles('test-org', 'test-repo', 'backend');

      assert.strictEqual(result.length, 2);
      assert.strictEqual(result[0].filename, '00-clean-architecture.mdc');
      assert.strictEqual(result[1].filename, '<EMAIL>');
    });

    test('should handle GitHub API errors gracefully', async () => {
      mockGithubService.fetchRepositoryContent.resolves({
        success: false,
        error: new Error('API Error')
      });

      try {
        await repositoryParser.discoverRuleFiles('test-org', 'test-repo', 'backend');
        assert.fail('Should have thrown an error');
      } catch (error) {
        assert.ok(error instanceof Error);
        assert.ok(error.message.includes('Failed to discover rule files'));
      }
    });

    test('should filter out non-mdc files', async () => {
      const mixedContent = [
        ...mockGithubContent,
        {
          name: 'README.md',
          path: 'backend/README.md',
          sha: 'readme123',
          size: 512,
          url: 'https://api.github.com/repos/test-org/test-repo/contents/backend/README.md',
          html_url: 'https://github.com/test-org/test-repo/blob/main/backend/README.md',
          git_url: 'https://api.github.com/repos/test-org/test-repo/git/blobs/readme123',
          download_url: 'https://api.github.com/repos/test-org/test-repo/contents/backend/README.md?ref=main',
          type: 'file' as const
        }
      ];

      mockGithubService.fetchRepositoryContent.resolves({
        success: true,
        data: mixedContent
      });

      mockRuleParser.parseRuleFilename.returns({
        filename: '00-clean-architecture.mdc',
        name: 'clean-architecture',
        category: RuleCategory.ARCHITECTURE,
        service: 'backend',
        path: 'backend/00-clean-architecture.mdc'
      });

      const result = await repositoryParser.discoverRuleFiles('test-org', 'test-repo', 'backend');

      // Should only include .mdc files, not README.md
      assert.strictEqual(result.length, 3); // Only the 3 .mdc files from mockGithubContent
    });
  });

  suite('validateRepositoryStructure', () => {
    test('should validate correct repository structure', () => {
      const validStructure = {
        services: {
          backend: {
            name: 'Backend Service',
            categories: [RuleCategory.ARCHITECTURE],
            ruleCounts: {
              [RuleCategory.ARCHITECTURE]: 1,
              [RuleCategory.WORKFLOWS]: 0,
              [RuleCategory.OPENAPI]: 0,
              [RuleCategory.DOMAIN_RULES]: 0
            }
          }
        },
        totalRules: 1,
        lastParsed: new Date()
      };

      const result = repositoryParser.validateRepositoryStructure(validStructure);
      assert.strictEqual(result, true);
    });

    test('should reject structure with no services', () => {
      const invalidStructure = {
        services: {},
        totalRules: 0,
        lastParsed: new Date()
      };

      const result = repositoryParser.validateRepositoryStructure(invalidStructure);
      assert.strictEqual(result, false);
    });

    test('should reject structure with invalid service', () => {
      const invalidStructure = {
        services: {
          backend: {
            // Missing required properties
          }
        },
        totalRules: 0,
        lastParsed: new Date()
      };

      const result = repositoryParser.validateRepositoryStructure(invalidStructure as any);
      assert.strictEqual(result, false);
    });
  });
});
