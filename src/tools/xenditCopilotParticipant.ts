import * as vscode from "vscode";
import type { IContextManager } from "../services/contextManager";
import type { ILogger } from "../services/logger";

/**
 * Register the xendit-copilot chat participant
 */
export function registerXenditCopilotChatParticipant(
  context: vscode.ExtensionContext,
  logger: <PERSON>ogger,
  contextManager: IContextManager
) {
  const handler: vscode.ChatRequestHandler = async (
    request: vscode.ChatRequest,
    chatContext: vscode.ChatContext,
    stream: vscode.ChatResponseStream,
    token: vscode.CancellationToken
  ) => {
    logger.info(`Xendit Copilot chat participant invoked with command: ${request.command}`);

    if (request.command === "list") {
      stream.markdown(
        `Available tools: ${vscode.lm.tools
          .map((tool) => tool.name)
          .join(", ")}\n\n`
      );
      return;
    }

    // Filter tools to include xendit-copilot tools and general tools
    const tools = vscode.lm.tools.filter((tool) =>
      tool.tags.includes("xendit-copilot") ||
      tool.tags.includes("chat-tools-sample")
    );

    logger.info(`Found ${tools.length} available tools: ${tools.map(t => t.name).join(", ")}`);

    // Get the best available model
    let model = request.model;
    if (model.vendor === "copilot" && model.family.startsWith("o1")) {
      // The o1 models do not currently support tools
      const models = await vscode.lm.selectChatModels({
        vendor: "copilot",
        family: "gpt-4o",
      });
      model = models[0];
    }

    const options: vscode.LanguageModelChatRequestOptions = {
      justification: "To provide context and knowledge from central repository",
      tools: tools,
    };

    // Create the system prompt for xendit-copilot
    const systemPrompt = `You are Xendit Copilot, an AI assistant that helps users find relevant information and context from Xendit's central knowledge repository.

You have access to tools that can retrieve relevant context and knowledge from a central repository. Use these tools when:
- Users ask questions about Xendit's products, services, or processes
- Users need information about specific topics, features, or documentation
- Users want to understand how things work or find relevant context
- Users are looking for specific information or knowledge

Always be helpful, accurate, and provide comprehensive answers based on the retrieved context.`;

    const messages: vscode.LanguageModelChatMessage[] = [
      vscode.LanguageModelChatMessage.User(systemPrompt),
      vscode.LanguageModelChatMessage.User(request.prompt)
    ];

    try {
      // Send the request to the language model
      const response = await model.sendRequest(messages, options, token);

      // Stream the response
      for await (const part of response.stream) {
        if (part instanceof vscode.LanguageModelTextPart) {
          stream.markdown(part.value);
        } else if (part instanceof vscode.LanguageModelToolCallPart) {
          // Tool calls are handled automatically by the language model
          logger.info(`Tool called: ${part.name}`);
        }
      }
    } catch (error) {
      logger.error("Error in xendit-copilot chat participant:", error);
      stream.markdown("❌ Sorry, I encountered an error while processing your request. Please try again.");
    }
  };

  const participant = vscode.chat.createChatParticipant(
    "chat.xendit-copilot",
    handler
  );

  participant.iconPath = new vscode.ThemeIcon("github");
  participant.followupProvider = {
    provideFollowups(result: vscode.ChatResult, context: vscode.ChatContext, token: vscode.CancellationToken) {
      return [
        {
          prompt: "Tell me about payment processing",
          label: "💳 Payment Processing",
          command: "payments"
        },
        {
          prompt: "How does user authentication work?",
          label: "🔐 Authentication",
          command: "auth"
        },
        {
          prompt: "Show me API documentation",
          label: "📚 API Documentation",
          command: "api"
        }
      ];
    }
  };

  context.subscriptions.push(participant);
  logger.info("Xendit Copilot chat participant registered successfully");
}
