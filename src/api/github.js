"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitHubApiService = void 0;
const pathUtils = __importStar(require("node:path"));
const vscode = __importStar(require("vscode"));
const githubApiResponseHandler_1 = require("../utils/githubApiResponseHandler");
function mapGitTypeToContentType(gitType) {
    switch (gitType) {
        case "blob":
            return "file";
        case "tree":
            return "dir";
        case "commit":
            return "submodule";
        default:
            return "file";
    }
}
class GitHubApiService {
    httpClient;
    rateLimitManager;
    logger;
    responseHandler;
    baseApiUrl = "https://api.github.com";
    constructor(httpClient, rateLimitManager, logger) {
        this.httpClient = httpClient;
        this.rateLimitManager = rateLimitManager;
        this.logger = logger;
        this.responseHandler = new githubApiResponseHandler_1.GitHubApiResponseHandler(this.rateLimitManager, this.logger);
    }
    async makeApiRequest(apiUrl, isRawContent = false) {
        const headers = {
            "User-Agent": "VS-Code-AIDD-Extension",
            Accept: isRawContent
                ? "application/vnd.github.raw"
                : "application/vnd.github.v3+json",
        };
        const configuration = vscode.workspace.getConfiguration("aidd");
        const token = configuration.get("githubToken");
        if (token) {
            headers.Authorization = `token ${token}`;
            this.logger.debug(`GitHub API Request (Auth): ${apiUrl}`);
        }
        else {
            this.logger.debug(`GitHub API Request (No Auth): ${apiUrl}`);
        }
        try {
            const httpResponse = await this.httpClient.get(apiUrl, headers);
            return this.responseHandler.handleResponse(httpResponse, isRawContent);
        }
        catch (error) {
            this.logger.error(`HTTP Client error for ${apiUrl}`, error);
            return {
                success: false,
                error: error instanceof Error
                    ? error
                    : new Error(`HTTP request failed: ${String(error)}`),
            };
        }
    }
    async fetchRepositoryContent(repository, path = "") {
        const { owner, name, branch } = repository;
        let apiUrl = `${this.baseApiUrl}/repos/${owner}/${name}/contents/${path}`;
        if (branch) {
            apiUrl += `?ref=${branch}`;
        }
        const result = await this.makeApiRequest(apiUrl);
        if (!result.success) {
            return result;
        }
        const data = Array.isArray(result.data) ? result.data : [result.data];
        return { success: true, data };
    }
    async fetchRepositoryContentRecursive(repository, _path = "", _maxDepth = Number.POSITIVE_INFINITY) {
        const { owner, name, branch = "main" } = repository;
        this.logger.info(`Fetching recursive tree for ${owner}/${name}, branch: ${branch}`);
        const branchApiUrl = `${this.baseApiUrl}/repos/${owner}/${name}/branches/${branch || "main"}`;
        const branchResult = await this.makeApiRequest(branchApiUrl);
        if (!branchResult.success) {
            this.logger.error(`Failed to fetch branch details for ${branch}`, branchResult.error);
            return {
                success: false,
                error: new Error(`Failed to get branch details: ${branchResult.error.message}`),
            };
        }
        const treeSha = branchResult.data.commit?.commit?.tree?.sha;
        if (!treeSha) {
            this.logger.error(`Could not find tree SHA for branch ${branch}`, branchResult.data);
            return {
                success: false,
                error: new Error("Could not find root tree SHA for the branch."),
            };
        }
        this.logger.debug(`Found root tree SHA: ${treeSha} for branch ${branch}`);
        const treeApiUrl = `${this.baseApiUrl}/repos/${owner}/${name}/git/trees/${treeSha}?recursive=1`;
        const treeResult = await this.makeApiRequest(treeApiUrl);
        if (!treeResult.success) {
            this.logger.error(`Failed to fetch recursive tree ${treeSha}`, treeResult.error);
            return {
                success: false,
                error: new Error(`Failed to get recursive tree: ${treeResult.error.message}`),
            };
        }
        const { tree, truncated } = treeResult.data;
        if (truncated) {
            this.logger.warn(`GitHub API response for recursive tree was truncated for ${owner}/${name}. Results may be incomplete.`);
        }
        const allContents = tree.map((item) => {
            const contentType = mapGitTypeToContentType(item.type);
            const itemName = pathUtils.basename(item.path);
            const itemHtmlUrl = `https://github.com/${owner}/${name}/${contentType === "file" ? "blob" : "tree"}/${branch}/${item.path}`;
            return {
                name: itemName,
                path: item.path,
                sha: item.sha,
                size: item.size ?? 0,
                url: `${this.baseApiUrl}/repos/${owner}/${name}/contents/${item.path}?ref=${branch}`,
                html_url: itemHtmlUrl,
                git_url: item.url ?? "",
                download_url: contentType === "file"
                    ? `${this.baseApiUrl}/repos/${owner}/${name}/contents/${item.path}?ref=${branch}`
                    : null,
                type: contentType,
            };
        });
        this.logger.info(`Recursive tree fetch complete for ${owner}/${name}. Found ${allContents.length} items. Truncated: ${truncated}`);
        return { success: true, data: allContents };
    }
    async fetchFileContent(downloadUrl) {
        return this.makeApiRequest(downloadUrl, true);
    }
}
exports.GitHubApiService = GitHubApiService;
//# sourceMappingURL=github.js.map