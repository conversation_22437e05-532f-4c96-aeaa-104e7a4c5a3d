"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoryParser = void 0;
const rules_1 = require("../types/rules");
/**
 * Service for parsing repository structure and discovering rules
 */
class RepositoryParser {
    logger;
    githubService;
    ruleParser;
    constructor(logger, githubService, ruleParser) {
        this.logger = logger;
        this.githubService = githubService;
        this.ruleParser = ruleParser;
    }
    /**
     * Parse repository structure from main.mdc and discover rules
     */
    async parseRepositoryStructure(owner, repo) {
        try {
            this.logger.info(`Parsing repository structure for ${owner}/${repo}`);
            // First, try to get main.mdc
            let structure;
            try {
                const repository = { owner, name: repo };
                const fileResult = await this.githubService.fetchRepositoryContent(repository, 'main.mdc');
                if (!fileResult.success || fileResult.data.length === 0) {
                    throw new Error('main.mdc not found');
                }
                const file = fileResult.data[0];
                if (!file.download_url) {
                    throw new Error('No download URL available for main.mdc');
                }
                const contentResult = await this.githubService.fetchFileContent(file.download_url);
                if (!contentResult.success) {
                    throw contentResult.error;
                }
                structure = this.parseMainMdc(contentResult.data);
            }
            catch (error) {
                this.logger.warn(`main.mdc not found, discovering structure automatically`);
                structure = await this.discoverRepositoryStructure(owner, repo);
            }
            // Discover rules for each service
            for (const [serviceName, serviceMapping] of Object.entries(structure.services)) {
                try {
                    const rules = await this.discoverRuleFiles(owner, repo, serviceName);
                    this.updateServiceMappingWithRules(serviceMapping, rules);
                }
                catch (error) {
                    this.logger.warn(`Failed to discover rules for service ${serviceName}: ${error}`);
                }
            }
            // Update total rule count
            structure.totalRules = Object.values(structure.services)
                .reduce((total, service) => total + Object.values(service.ruleCounts)
                .reduce((sum, count) => sum + count, 0), 0);
            structure.lastParsed = new Date();
            structure.repositoryUrl = `https://github.com/${owner}/${repo}`;
            this.logger.info(`Repository structure parsed successfully. Found ${structure.totalRules} rules across ${Object.keys(structure.services).length} services`);
            return structure;
        }
        catch (error) {
            this.logger.error(`Error parsing repository structure for ${owner}/${repo}:`, error);
            throw this.createRuleSystemError(rules_1.RuleSystemError.MAIN_MDC_PARSE_ERROR, `Failed to parse repository structure: ${owner}/${repo}`, { owner, repo }, error);
        }
    }
    /**
     * Discover rule files in a service directory
     */
    async discoverRuleFiles(owner, repo, servicePath) {
        try {
            this.logger.debug(`Discovering rule files in ${servicePath}`);
            const repository = { owner, name: repo };
            const result = await this.githubService.fetchRepositoryContent(repository, servicePath);
            if (!result.success) {
                throw result.error;
            }
            const files = result.data;
            const rules = [];
            for (const file of files) {
                if (file.type === 'file' && file.name.endsWith('.mdc') && file.name !== 'main.mdc') {
                    const metadata = this.ruleParser.parseRuleFilename(file.name, servicePath, file.path);
                    if (metadata) {
                        rules.push(metadata);
                    }
                }
            }
            this.logger.debug(`Found ${rules.length} rule files in ${servicePath}`);
            return rules;
        }
        catch (error) {
            this.logger.error(`Error discovering rule files in ${servicePath}:`, error);
            throw this.createRuleSystemError(rules_1.RuleSystemError.RULE_FILE_NOT_FOUND, `Failed to discover rule files in service: ${servicePath}`, { servicePath }, error);
        }
    }
    /**
     * Parse main.mdc file content
     */
    parseMainMdc(content) {
        try {
            this.logger.debug('Parsing main.mdc content');
            const structure = {
                services: {},
                totalRules: 0,
                lastParsed: new Date(),
            };
            // Extract description from first paragraph
            const lines = content.split('\n');
            const descriptionLines = [];
            let inDescription = true;
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine && inDescription) {
                    break;
                }
                if (trimmedLine && !trimmedLine.startsWith('#') && inDescription) {
                    descriptionLines.push(trimmedLine);
                }
                else if (trimmedLine.startsWith('#')) {
                    inDescription = false;
                }
            }
            if (descriptionLines.length > 0) {
                structure.description = descriptionLines.join(' ').trim();
            }
            // Parse service mappings (simplified - could be enhanced with more sophisticated parsing)
            const servicePattern = /##\s+([^#\n]+)/g;
            let match;
            while ((match = servicePattern.exec(content)) !== null) {
                const serviceName = match[1].trim().toLowerCase().replace(/\s+/g, '-');
                structure.services[serviceName] = {
                    name: match[1].trim(),
                    categories: [rules_1.RuleCategory.ARCHITECTURE, rules_1.RuleCategory.WORKFLOWS, rules_1.RuleCategory.OPENAPI, rules_1.RuleCategory.DOMAIN_RULES],
                    ruleCounts: {
                        [rules_1.RuleCategory.ARCHITECTURE]: 0,
                        [rules_1.RuleCategory.WORKFLOWS]: 0,
                        [rules_1.RuleCategory.OPENAPI]: 0,
                        [rules_1.RuleCategory.DOMAIN_RULES]: 0,
                    },
                };
            }
            this.logger.debug(`Parsed main.mdc: found ${Object.keys(structure.services).length} services`);
            return structure;
        }
        catch (error) {
            this.logger.error('Error parsing main.mdc content:', error);
            throw this.createRuleSystemError(rules_1.RuleSystemError.MAIN_MDC_PARSE_ERROR, 'Failed to parse main.mdc content', { contentLength: content.length }, error);
        }
    }
    /**
     * Validate repository structure
     */
    validateRepositoryStructure(structure) {
        try {
            if (!structure.services || Object.keys(structure.services).length === 0) {
                return false;
            }
            for (const [serviceName, service] of Object.entries(structure.services)) {
                if (!service.name || !service.categories || !service.ruleCounts) {
                    this.logger.warn(`Invalid service structure for ${serviceName}`);
                    return false;
                }
            }
            return true;
        }
        catch (error) {
            this.logger.error('Error validating repository structure:', error);
            return false;
        }
    }
    /**
     * Discover repository structure automatically when main.mdc is not available
     */
    async discoverRepositoryStructure(owner, repo) {
        this.logger.info('Discovering repository structure automatically');
        const structure = {
            description: `Auto-discovered structure for ${owner}/${repo}`,
            services: {},
            totalRules: 0,
            lastParsed: new Date(),
        };
        try {
            const repository = { owner, name: repo };
            const result = await this.githubService.fetchRepositoryContent(repository, '');
            if (result.success) {
                const rootContents = result.data;
                for (const item of rootContents) {
                    if (item.type === 'dir' && !item.name.startsWith('.')) {
                        structure.services[item.name] = {
                            name: item.name,
                            categories: [rules_1.RuleCategory.ARCHITECTURE, rules_1.RuleCategory.WORKFLOWS, rules_1.RuleCategory.OPENAPI, rules_1.RuleCategory.DOMAIN_RULES],
                            ruleCounts: {
                                [rules_1.RuleCategory.ARCHITECTURE]: 0,
                                [rules_1.RuleCategory.WORKFLOWS]: 0,
                                [rules_1.RuleCategory.OPENAPI]: 0,
                                [rules_1.RuleCategory.DOMAIN_RULES]: 0,
                            },
                        };
                    }
                }
            }
        }
        catch (error) {
            this.logger.warn(`Failed to auto-discover repository structure: ${error}`);
        }
        return structure;
    }
    /**
     * Update service mapping with discovered rules
     */
    updateServiceMappingWithRules(serviceMapping, rules) {
        // Reset counts
        serviceMapping.ruleCounts = {
            [rules_1.RuleCategory.ARCHITECTURE]: 0,
            [rules_1.RuleCategory.WORKFLOWS]: 0,
            [rules_1.RuleCategory.OPENAPI]: 0,
            [rules_1.RuleCategory.DOMAIN_RULES]: 0,
        };
        // Count rules by category
        for (const rule of rules) {
            serviceMapping.ruleCounts[rule.category]++;
        }
        // Update available categories based on actual rules
        serviceMapping.categories = Object.entries(serviceMapping.ruleCounts)
            .filter(([, count]) => count > 0)
            .map(([category]) => parseInt(category, 10));
    }
    /**
     * Create a structured rule system error
     */
    createRuleSystemError(type, message, context, originalError) {
        const error = new Error(message);
        error.type = type;
        error.context = context;
        error.originalError = originalError;
        return error;
    }
}
exports.RepositoryParser = RepositoryParser;
//# sourceMappingURL=repositoryParser.js.map