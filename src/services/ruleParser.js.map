{"version": 3, "file": "ruleParser.js", "sourceRoot": "", "sources": ["ruleParser.ts"], "names": [], "mappings": ";;;AAAA,0CAMwB;AA4BxB;;GAEG;AACH,MAAa,UAAU;IACJ,MAAM,CAAU;IAEjC,kEAAkE;IACjD,qBAAqB,GAAG,oDAAoD,CAAC;IAE9F,YAAY,MAAe;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB,EAAE,OAAe,EAAE,IAAY;QAC/D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,QAAQ,EAAE,CAAC,CAAC;gBAC9D,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,GAAG,KAAK,CAAC;YAC1D,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAE9C,2BAA2B;YAC3B,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,QAAQ,EAAE,CAAC,CAAC;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,QAAQ,GAAG,WAA2B,CAAC;YAE7C,MAAM,QAAQ,GAAkB;gBAC9B,QAAQ;gBACR,QAAQ;gBACR,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBAC9B,IAAI;gBACJ,OAAO;aACR,CAAC;YAEF,IAAI,OAAO,EAAE,CAAC;gBACZ,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;YAC7B,CAAC;YAED,IAAI,WAAW,EAAE,CAAC;gBAChB,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;YACrC,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACvF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAuB,EAAE,OAAe;QACvD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,WAAW,GAAiB;gBAChC,GAAG,QAAQ;gBACX,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;gBACvB,OAAO;gBACP,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,QAAQ,CAAC,QAAQ,aAAa,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7F,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,QAAQ,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,IAAI,CAAC,qBAAqB,CAC9B,uBAAe,CAAC,qBAAqB,EACrC,iCAAiC,QAAQ,CAAC,QAAQ,EAAE,EACpD,EAAE,QAAQ,EAAE,EACZ,KAAc,CACf,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAgB;QACnC,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAe;QAC5B,IAAI,CAAC;YACH,yCAAyC;YACzC,MAAM,YAAY,GAAG,OAAO;iBACzB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,iBAAiB;iBACzC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,cAAc;iBAC9C,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,gBAAgB;iBAC5C,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,qBAAqB;iBAC/C,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,qBAAqB;iBACpD,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,0BAA0B;iBAClE,IAAI,EAAE,CAAC;YAEV,8CAA8C;YAC9C,MAAM,cAAc,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,GAAG,GAAG;gBACzC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;gBAC1C,CAAC,CAAC,cAAc,CAAC;YAEnB,OAAO,OAAO,IAAI,sBAAsB,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAC;YACpE,OAAO,2BAA2B,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,IAAY;QAChC,OAAO,IAAI;aACR,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;aAClB,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,qBAAqB,CAC3B,IAAqB,EACrB,OAAe,EACf,OAAiC,EACjC,aAAqB;QAErB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAqB,CAAC;QACrD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACxB,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;QACpC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,WAAmB;QAC5C,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YACzC,OAAO,WAA2B,CAAC;QACrC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAsB;QAClD,MAAM,KAAK,GAAG;YACZ,CAAC,oBAAY,CAAC,YAAY,CAAC,EAAE,cAAc;YAC3C,CAAC,oBAAY,CAAC,SAAS,CAAC,EAAE,WAAW;YACrC,CAAC,oBAAY,CAAC,OAAO,CAAC,EAAE,SAAS;YACjC,CAAC,oBAAY,CAAC,YAAY,CAAC,EAAE,cAAc;SAC5C,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,QAAuB;QAC/C,MAAM,KAAK,GAAG;YACZ,KAAK,QAAQ,CAAC,IAAI,IAAI;YACtB,aAAa,UAAU,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACnE,YAAY,QAAQ,CAAC,OAAO,EAAE;SAC/B,CAAC;QAEF,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,KAAK,CAAC,IAAI,CAAC,YAAY,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,CAAC,UAAU,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC;QAE7C,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;CACF;AA5LD,gCA4LC"}