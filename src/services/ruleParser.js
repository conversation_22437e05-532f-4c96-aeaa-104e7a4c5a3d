"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleParser = void 0;
const rules_1 = require("../types/rules");
/**
 * Service for parsing rule files and extracting metadata
 */
class RuleParser {
    logger;
    // Rule filename pattern: ##-rule-name[@version][-specificity].mdc
    RULE_FILENAME_PATTERN = /^(\d{2})-([^@\-]+)(?:@([^-]+))?(?:-([^.]+))?\.mdc$/;
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Parse rule filename to extract metadata
     */
    parseRuleFilename(filename, service, path) {
        try {
            const match = this.RULE_FILENAME_PATTERN.exec(filename);
            if (!match) {
                this.logger.warn(`Invalid rule filename format: ${filename}`);
                return null;
            }
            const [, categoryStr, name, version, specificity] = match;
            const categoryNum = parseInt(categoryStr, 10);
            // Validate category number
            if (categoryNum < 0 || categoryNum > 3) {
                this.logger.warn(`Invalid category number in filename: ${filename}`);
                return null;
            }
            const category = categoryNum;
            const metadata = {
                filename,
                category,
                name: this.cleanRuleName(name),
                path,
                service,
            };
            if (version) {
                metadata.version = version;
            }
            if (specificity) {
                metadata.specificity = specificity;
            }
            this.logger.debug(`Parsed rule metadata for ${filename}: ${JSON.stringify(metadata)}`);
            return metadata;
        }
        catch (error) {
            this.logger.error(`Error parsing rule filename ${filename}:`, error);
            return null;
        }
    }
    /**
     * Parse rule content from markdown
     */
    parseRuleContent(metadata, content) {
        try {
            const summary = this.extractSummary(content);
            const ruleContent = {
                ...metadata,
                content: content.trim(),
                summary,
                lastModified: new Date(),
            };
            this.logger.debug(`Parsed rule content for ${metadata.filename}, length: ${content.length}`);
            return ruleContent;
        }
        catch (error) {
            this.logger.error(`Error parsing rule content for ${metadata.filename}:`, error);
            throw this.createRuleSystemError(rules_1.RuleSystemError.RULE_FILE_PARSE_ERROR, `Failed to parse rule content: ${metadata.filename}`, { metadata }, error);
        }
    }
    /**
     * Validate rule filename format
     */
    validateRuleFilename(filename) {
        return this.RULE_FILENAME_PATTERN.test(filename);
    }
    /**
     * Extract summary from rule content
     */
    extractSummary(content) {
        try {
            // Remove markdown headers and formatting
            const cleanContent = content
                .replace(/^#+\s*/gm, '') // Remove headers
                .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
                .replace(/\*(.*?)\*/g, '$1') // Remove italic
                .replace(/`(.*?)`/g, '$1') // Remove inline code
                .replace(/```[\s\S]*?```/g, '') // Remove code blocks
                .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
                .trim();
            // Get first paragraph or first 200 characters
            const firstParagraph = cleanContent.split('\n\n')[0];
            const summary = firstParagraph.length > 200
                ? firstParagraph.substring(0, 200) + '...'
                : firstParagraph;
            return summary || 'No summary available';
        }
        catch (error) {
            this.logger.warn(`Error extracting summary from content: ${error}`);
            return 'Summary extraction failed';
        }
    }
    /**
     * Clean rule name by replacing hyphens with spaces and capitalizing
     */
    cleanRuleName(name) {
        return name
            .replace(/-/g, ' ')
            .replace(/\b\w/g, (char) => char.toUpperCase());
    }
    /**
     * Create a structured rule system error
     */
    createRuleSystemError(type, message, context, originalError) {
        const error = new Error(message);
        error.type = type;
        error.context = context;
        error.originalError = originalError;
        return error;
    }
    /**
     * Parse category number to RuleCategory enum
     */
    static parseCategoryNumber(categoryNum) {
        if (categoryNum >= 0 && categoryNum <= 3) {
            return categoryNum;
        }
        return null;
    }
    /**
     * Get category display name
     */
    static getCategoryDisplayName(category) {
        const names = {
            [rules_1.RuleCategory.ARCHITECTURE]: 'Architecture',
            [rules_1.RuleCategory.WORKFLOWS]: 'Workflows',
            [rules_1.RuleCategory.OPENAPI]: 'OpenAPI',
            [rules_1.RuleCategory.DOMAIN_RULES]: 'Domain Rules',
        };
        return names[category];
    }
    /**
     * Format rule metadata for display
     */
    static formatRuleMetadata(metadata) {
        const parts = [
            `**${metadata.name}**`,
            `Category: ${RuleParser.getCategoryDisplayName(metadata.category)}`,
            `Service: ${metadata.service}`,
        ];
        if (metadata.version) {
            parts.push(`Version: ${metadata.version}`);
        }
        if (metadata.specificity) {
            parts.push(`Scope: ${metadata.specificity}`);
        }
        parts.push(`File: \`${metadata.filename}\``);
        return parts.join(' | ');
    }
}
exports.RuleParser = RuleParser;
//# sourceMappingURL=ruleParser.js.map