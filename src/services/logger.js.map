{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["logger.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAWjC,MAAa,MAAM;IACT,aAAa,CAAuB;IACpC,SAAS,CAAU;IAE3B,YAAY,WAAmB,EAAE,SAAS,GAAG,KAAK;QAChD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACpE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,GAAG,CAAC;IAChD,CAAC;IAEM,GAAG,CAAC,OAAe;QACxB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;IACrE,CAAC;IAEM,KAAK,CAAC,OAAe,EAAE,KAAe;QAC3C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,OAAO,EAAE,CAAC,CAAC;QAE1E,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAClD,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAChB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACvD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAEM,IAAI,CAAC,OAAe;QACzB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,UAAU,OAAO,EAAE,CAAC,CAAC;IAC3E,CAAC;IAEM,IAAI,CAAC,OAAe;QACzB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,aAAa,OAAO,EAAE,CAAC,CAAC;IAC9E,CAAC;IAEM,KAAK,CAAC,OAAe;QAC1B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC5B,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;CACF;AAvDD,wBAuDC"}