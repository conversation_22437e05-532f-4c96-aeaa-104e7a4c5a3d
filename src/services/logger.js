"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const vscode = __importStar(require("vscode"));
class Logger {
    outputChannel;
    debugMode;
    constructor(channelName, debugMode = false) {
        this.outputChannel = vscode.window.createOutputChannel(channelName);
        this.debugMode = debugMode;
    }
    getTimestamp() {
        return `[${new Date().toLocaleTimeString()}]`;
    }
    log(message) {
        this.outputChannel.appendLine(`${this.getTimestamp()} ${message}`);
    }
    error(message, error) {
        this.outputChannel.appendLine(`${this.getTimestamp()} ERROR: ${message}`);
        if (error) {
            if (error instanceof Error) {
                this.outputChannel.appendLine(`${error.message}`);
                if (error.stack) {
                    this.outputChannel.appendLine(error.stack);
                }
            }
            else if (typeof error === "object" && error !== null) {
                this.outputChannel.appendLine(JSON.stringify(error, null, 2));
            }
            else {
                this.outputChannel.appendLine(String(error));
            }
        }
    }
    info(message) {
        this.outputChannel.appendLine(`${this.getTimestamp()} INFO: ${message}`);
    }
    warn(message) {
        this.outputChannel.appendLine(`${this.getTimestamp()} WARNING: ${message}`);
    }
    debug(message) {
        if (this.debugMode) {
            this.outputChannel.appendLine(`${this.getTimestamp()} DEBUG: ${message}`);
        }
    }
    show() {
        this.outputChannel.show();
    }
    dispose() {
        this.outputChannel.dispose();
    }
}
exports.Logger = Logger;
//# sourceMappingURL=logger.js.map