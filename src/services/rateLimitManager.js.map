{"version": 3, "file": "rateLimitManager.js", "sourceRoot": "", "sources": ["rateLimitManager.ts"], "names": [], "mappings": ";;;AAWA,MAAa,gBAAgB;IAGE;IAFrB,gBAAgB,GAA2B,IAAI,CAAC;IAExD,YAA6B,MAAe;QAAf,WAAM,GAAN,MAAM,CAAS;IAAG,CAAC;IAEzC,iBAAiB,CAAC,OAAiC;QACxD,MAAM,KAAK,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAE3C,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAoB;gBAChC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;gBACnE,SAAS,EAAE,MAAM,CAAC,QAAQ,CACxB,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EACnD,EAAE,CACH;gBACD,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;aACpE,CAAC;YAEF,IACE,CAAC,IAAI,CAAC,gBAAgB;gBACtB,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB,CAAC,SAAS;gBACtD,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAC9C,CAAC;gBACD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uBAAuB,IAAI,CAAC,gBAAgB,CAAC,SAAS,IACpD,IAAI,CAAC,gBAAgB,CAAC,KACxB,cAAc,IAAI,CAAC,YAAY,EAAE,EAAE,kBAAkB,EAAE,EAAE,CAC1D,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEM,eAAe;QACpB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YACnE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC1C,OAAO,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAC3C,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEM,YAAY;QACjB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAvDD,4CAuDC"}