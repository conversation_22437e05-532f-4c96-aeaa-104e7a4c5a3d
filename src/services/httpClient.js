"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpClient = void 0;
const fs = __importStar(require("node:fs"));
const https = __importStar(require("node:https"));
const node_url_1 = require("node:url");
class HttpClient {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    get(url, headers = {}) {
        return new Promise((resolve, reject) => {
            const parsedUrl = new node_url_1.URL(url);
            const options = {
                headers: {
                    ...headers,
                },
                timeout: 30000,
            };
            this.logger.debug(`HttpClient GET: ${url}`);
            const request = https.get(parsedUrl, options, (res) => {
                let data = "";
                res.setEncoding("utf8");
                res.on("data", (chunk) => {
                    data += chunk;
                });
                res.on("end", () => {
                    this.logger.debug(`HttpClient Response: ${res.statusCode} from ${url}`);
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                    });
                });
            });
            request.on("error", (error) => {
                this.logger.error(`HttpClient Error for ${url}: ${error.message}`, error);
                reject(error);
            });
            request.on("timeout", () => {
                request.destroy();
                this.logger.error(`HttpClient Timeout for ${url}`);
                reject(new Error(`Request timed out after ${options.timeout}ms`));
            });
        });
    }
    downloadFile(url, targetPath, headers = {}) {
        return new Promise((resolve, reject) => {
            const parsedUrl = new node_url_1.URL(url);
            const options = {
                headers: {
                    ...headers,
                    "User-Agent": "VS-Code-AIDD-Extension",
                },
                timeout: 60000,
            };
            this.logger.debug(`HttpClient DownloadFile: ${url} to ${targetPath}`);
            const request = https.get(parsedUrl, options, (response) => {
                if (response.statusCode === 200) {
                    const fileStream = fs.createWriteStream(targetPath);
                    response.pipe(fileStream);
                    fileStream.on("finish", () => {
                        fileStream.close();
                        this.logger.debug(`Successfully downloaded file to ${targetPath}`);
                        resolve();
                        /*
                         fileStream.close((err) => {
                           if (err) {
                             this.logger.error(
                               `Error closing file stream for ${targetPath}`,
                               err,
                             );
                             reject(err);
                           } else {
                             this.logger.debug(
                               `Successfully downloaded file to ${targetPath}`,
                             );
                             resolve();
                           }
                         });
                         */
                    });
                    fileStream.on("error", (err) => {
                        this.logger.error(`File stream error for ${targetPath}`, err.message);
                        fs.unlink(targetPath, (unlinkErr) => {
                            if (unlinkErr) {
                                this.logger.error(`Failed to delete incomplete file ${targetPath}`, unlinkErr);
                            }
                            reject(err);
                        });
                    });
                }
                else if (response.statusCode === 302 ||
                    response.statusCode === 301) {
                    const redirectUrl = response.headers.location;
                    if (redirectUrl) {
                        this.logger.debug(`Redirecting download from ${url} to ${redirectUrl}`);
                        this.downloadFile(redirectUrl, targetPath)
                            .then(resolve)
                            .catch(reject);
                    }
                    else {
                        const errorMsg = `Redirect (${response.statusCode}) without location header from ${url}`;
                        this.logger.error(errorMsg);
                        reject(new Error(errorMsg));
                    }
                }
                else {
                    const errorMsg = `Failed to download file from ${url}: Status Code ${response.statusCode}`;
                    this.logger.error(errorMsg);
                    response.resume();
                    reject(new Error(errorMsg));
                }
            });
            request.on("error", (error) => {
                this.logger.error(`HttpClient DownloadFile Error for ${url}: ${error.message}`, error);
                reject(error);
            });
            request.on("timeout", () => {
                request.destroy();
                const errorMsg = `DownloadFile request timed out after ${options.timeout}ms for ${url}`;
                this.logger.error(errorMsg);
                reject(new Error(errorMsg));
            });
        });
    }
}
exports.HttpClient = HttpClient;
//# sourceMappingURL=httpClient.js.map