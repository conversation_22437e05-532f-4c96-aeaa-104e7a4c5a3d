{"version": 3, "file": "contextManager.js", "sourceRoot": "", "sources": ["contextManager.ts"], "names": [], "mappings": ";;;AACA,0CAUwB;AAExB,yDAA8E;AAC9E,6CAA4D;AA2C5D;;GAEG;AACH,MAAa,cAAc;IACR,MAAM,CAAU;IAChB,aAAa,CAAoB;IACjC,cAAc,CAAkB;IAChC,gBAAgB,CAAoB;IACpC,UAAU,CAAc;IAEzC,kDAAkD;IAC1C,wBAAwB,GAAsC,IAAI,GAAG,EAAE,CAAC;IACxE,gBAAgB,GAA8B,IAAI,GAAG,EAAE,CAAC;IACxD,QAAQ,GAAiC,IAAI,GAAG,EAAE,CAAC;IAE3D,YACE,MAAe,EACf,aAAgC,EAChC,cAA+B;QAE/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,UAAwB,CAAC,CAAC;IACrG,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,MAA+B;QACnD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;YAEpE,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChE,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,qBAAqB,CAC9B,uBAAe,CAAC,oBAAoB,EACpC,mCAAmC,CACpC,CAAC;YACJ,CAAC;YAED,yCAAyC;YACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAEnD,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAElD,oCAAoC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAEpE,gBAAgB;YAChB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;YACtC,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YAEpD,gCAAgC;YAChC,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,KAAK,KAAK;gBAC3C,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9E,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAoB,CAAC,CAAC;YAEzD,MAAM,MAAM,GAA4B;gBACtC,KAAK;gBACL,UAAU,EAAE,QAAQ,CAAC,MAAM;gBAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,MAAM,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC;gBAC9C,mBAAmB;aACpB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,KAAK,CAAC,MAAM,iBAAiB,QAAQ,CAAC,MAAM,QAAQ,CAAC,CAAC;YACpF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;YACzD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,QAAQ,GAAG,GAAG,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtD,oBAAoB;YACpB,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;gBAC5D,yCAAyC;gBACzC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;oBACvD,OAAO,MAAM,CAAC;gBAChB,CAAC;YACH,CAAC;YAED,6BAA6B;YAC7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CACpE,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,IAAI,CACd,CAAC;YAEF,mBAAmB;YACnB,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAEvD,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAA6B;QAC7C,IAAI,CAAC;YACH,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChE,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,KAAK,GAAmB,EAAE,CAAC;YACjC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO;gBACvC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACpB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAE9C,KAAK,MAAM,WAAW,IAAI,gBAAgB,EAAE,CAAC;gBAC3C,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAC1D,IAAI,CAAC,OAAO;oBAAE,SAAS;gBAEvB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAG,CAAC;oBAC1D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAChE,QAAQ,CAAC,KAAK,EACd,QAAQ,CAAC,IAAI,EACb,WAAW,CACZ,CAAC;oBAEF,KAAK,MAAM,YAAY,IAAI,YAAY,EAAE,CAAC;wBACxC,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;4BACvD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;4BAC5D,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAC1B,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,WAAW,KAAK,KAAK,EAAE,CAAC,CAAC;gBAChF,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAuB;QAC1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,GAAG,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAE5D,oBAAoB;YACpB,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;YAC9C,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAG,CAAC;YAE1D,8CAA8C;YAC9C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAChE,QAAQ,EACR,QAAQ,CAAC,IAAI,CACd,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxD,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,uCAAuC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,aAAa,CAAC,KAAK,CAAC;YAC5B,CAAC;YAED,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC;YAEnC,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAExE,mBAAmB;YACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAEjD,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,QAAQ,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,IAAI,CAAC,qBAAqB,CAC9B,uBAAe,CAAC,mBAAmB,EACnC,+BAA+B,QAAQ,CAAC,QAAQ,EAAE,EAClD,EAAE,QAAQ,EAAE,EACZ,KAAc,CACf,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,MAAM,SAAS,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACrF,MAAM,OAAO,GAAoB;YAC/B,EAAE,EAAE,SAAS;YACb,cAAc,EAAE,IAAI,GAAG,EAAE;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;QAE/D,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAA+B;QAC1D,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK;aAC1B,WAAW,EAAE;aACb,KAAK,CAAC,KAAK,CAAC;aACZ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEnC,OAAO;YACL,QAAQ;YACR,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,aAAa,EAAE,IAAI;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,QAAuB,EAAE,QAA6B;QAClF,kBAAkB;QAClB,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC/E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iBAAiB;QACjB,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iBAAiB;QACjB,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,qBAAqB;QACrB,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,mBAAmB;QACnB,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;YACzE,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YACjF,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAqB,EAAE,KAAa;QAC7D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE3E,OAAO,KAAK;aACT,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,IAAI;YACJ,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC;SAClE,CAAC,CAAC;aACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,IAAkB,EAAE,UAAoB,EAAE,UAAkB;QAC1F,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,mBAAmB;QACnB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,KAAK,IAAI,GAAG,CAAC;QACf,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3C,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAChD,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC9B,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,KAAK,IAAI,EAAE,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAChD,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC9B,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;gBACzE,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAED,yCAAyC;QACzC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,cAAc,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAC1F,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,qBAAqB,CAC3B,IAAqB,EACrB,OAAe,EACf,OAAiC,EACjC,aAAqB;QAErB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAqB,CAAC;QACrD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACxB,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;QACpC,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAnXD,wCAmXC"}