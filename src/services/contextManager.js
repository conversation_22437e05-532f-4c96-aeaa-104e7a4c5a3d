"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextManager = void 0;
const rules_1 = require("../types/rules");
const repositoryParser_1 = require("./repositoryParser");
const ruleParser_1 = require("./ruleParser");
/**
 * Service for managing context retrieval and session persistence
 */
class ContextManager {
    logger;
    githubService;
    storageService;
    repositoryParser;
    ruleParser;
    // Cache for repository structure and rule content
    repositoryStructureCache = new Map();
    ruleContentCache = new Map();
    sessions = new Map();
    constructor(logger, githubService, storageService) {
        this.logger = logger;
        this.githubService = githubService;
        this.storageService = storageService;
        this.ruleParser = new ruleParser_1.RuleParser(logger);
        this.repositoryParser = new repositoryParser_1.RepositoryParser(logger, githubService, this.ruleParser);
    }
    /**
     * Retrieve relevant context based on user query
     */
    async retrieveContext(params) {
        try {
            this.logger.info(`Retrieving context for query: "${params.query}"`);
            const repositoryStructure = await this.getRepositoryStructure();
            if (!repositoryStructure) {
                throw this.createRuleSystemError(rules_1.RuleSystemError.REPOSITORY_NOT_FOUND, 'No repository structure available');
            }
            // Parse query and create search criteria
            const criteria = this.parseQueryToCriteria(params);
            // Search for relevant rules
            const allRules = await this.searchRules(criteria);
            // Score and rank rules by relevance
            const scoredRules = this.scoreRuleRelevance(allRules, params.query);
            // Limit results
            const maxRules = params.maxRules || 5;
            const limitedRules = scoredRules.slice(0, maxRules);
            // Get rule content if requested
            const rules = params.includeContent !== false
                ? await Promise.all(limitedRules.map(({ rule }) => this.getRuleContent(rule)))
                : limitedRules.map(({ rule }) => rule);
            const result = {
                rules,
                totalFound: allRules.length,
                query: params.query,
                scores: limitedRules.map(({ score }) => score),
                repositoryStructure,
            };
            this.logger.info(`Retrieved ${rules.length} rules out of ${allRules.length} found`);
            return result;
        }
        catch (error) {
            this.logger.error('Error retrieving context:', error);
            throw error;
        }
    }
    /**
     * Get repository structure for current repository
     */
    async getRepositoryStructure() {
        try {
            const lastRepo = this.storageService.getLastRepository();
            if (!lastRepo) {
                this.logger.warn('No repository selected');
                return null;
            }
            const cacheKey = `${lastRepo.owner}/${lastRepo.name}`;
            // Check cache first
            if (this.repositoryStructureCache.has(cacheKey)) {
                const cached = this.repositoryStructureCache.get(cacheKey);
                // Check if cache is still valid (1 hour)
                if (Date.now() - cached.lastParsed.getTime() < 3600000) {
                    return cached;
                }
            }
            // Parse repository structure
            const structure = await this.repositoryParser.parseRepositoryStructure(lastRepo.owner, lastRepo.name);
            // Cache the result
            this.repositoryStructureCache.set(cacheKey, structure);
            return structure;
        }
        catch (error) {
            this.logger.error('Error getting repository structure:', error);
            return null;
        }
    }
    /**
     * Search rules based on criteria
     */
    async searchRules(criteria) {
        try {
            const repositoryStructure = await this.getRepositoryStructure();
            if (!repositoryStructure) {
                return [];
            }
            const rules = [];
            const servicesToSearch = criteria.service
                ? [criteria.service]
                : Object.keys(repositoryStructure.services);
            for (const serviceName of servicesToSearch) {
                const service = repositoryStructure.services[serviceName];
                if (!service)
                    continue;
                try {
                    const lastRepo = this.storageService.getLastRepository();
                    const serviceRules = await this.repositoryParser.discoverRuleFiles(lastRepo.owner, lastRepo.name, serviceName);
                    for (const ruleMetadata of serviceRules) {
                        if (this.matchesSearchCriteria(ruleMetadata, criteria)) {
                            const ruleContent = await this.getRuleContent(ruleMetadata);
                            rules.push(ruleContent);
                        }
                    }
                }
                catch (error) {
                    this.logger.warn(`Error searching rules in service ${serviceName}: ${error}`);
                }
            }
            return rules;
        }
        catch (error) {
            this.logger.error('Error searching rules:', error);
            return [];
        }
    }
    /**
     * Get rule content by metadata
     */
    async getRuleContent(metadata) {
        try {
            const cacheKey = `${metadata.service}/${metadata.filename}`;
            // Check cache first
            if (this.ruleContentCache.has(cacheKey)) {
                return this.ruleContentCache.get(cacheKey);
            }
            const lastRepo = this.storageService.getLastRepository();
            // Get the file content using the download URL
            const fileResult = await this.githubService.fetchRepositoryContent(lastRepo, metadata.path);
            if (!fileResult.success || fileResult.data.length === 0) {
                throw new Error(`File not found: ${metadata.path}`);
            }
            const file = fileResult.data[0];
            if (!file.download_url) {
                throw new Error(`No download URL available for file: ${metadata.path}`);
            }
            const contentResult = await this.githubService.fetchFileContent(file.download_url);
            if (!contentResult.success) {
                throw contentResult.error;
            }
            const content = contentResult.data;
            const ruleContent = this.ruleParser.parseRuleContent(metadata, content);
            // Cache the result
            this.ruleContentCache.set(cacheKey, ruleContent);
            return ruleContent;
        }
        catch (error) {
            this.logger.error(`Error getting rule content for ${metadata.filename}:`, error);
            throw this.createRuleSystemError(rules_1.RuleSystemError.RULE_FILE_NOT_FOUND, `Failed to get rule content: ${metadata.filename}`, { metadata }, error);
        }
    }
    /**
     * Clear cached data
     */
    clearCache() {
        this.repositoryStructureCache.clear();
        this.ruleContentCache.clear();
        this.logger.info('Context manager cache cleared');
    }
    /**
     * Initialize context session
     */
    initializeSession() {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const session = {
            id: sessionId,
            retrievedRules: new Set(),
            startTime: new Date(),
            lastActivity: new Date(),
        };
        this.sessions.set(sessionId, session);
        this.logger.debug(`Initialized context session: ${sessionId}`);
        return sessionId;
    }
    /**
     * Get context session
     */
    getSession(sessionId) {
        return this.sessions.get(sessionId) || null;
    }
    /**
     * Parse user query to search criteria
     */
    parseQueryToCriteria(params) {
        const keywords = params.query
            .toLowerCase()
            .split(/\s+/)
            .filter(word => word.length > 2);
        return {
            keywords,
            category: params.category,
            service: params.service,
            searchContent: true,
        };
    }
    /**
     * Check if rule metadata matches search criteria
     */
    matchesSearchCriteria(metadata, criteria) {
        // Category filter
        if (criteria.category !== undefined && metadata.category !== criteria.category) {
            return false;
        }
        // Service filter
        if (criteria.service && metadata.service !== criteria.service) {
            return false;
        }
        // Version filter
        if (criteria.version && metadata.version !== criteria.version) {
            return false;
        }
        // Specificity filter
        if (criteria.specificity && metadata.specificity !== criteria.specificity) {
            return false;
        }
        // Keyword matching
        if (criteria.keywords.length > 0) {
            const searchText = `${metadata.name} ${metadata.filename}`.toLowerCase();
            const hasMatch = criteria.keywords.some(keyword => searchText.includes(keyword));
            if (!hasMatch) {
                return false;
            }
        }
        return true;
    }
    /**
     * Score rule relevance based on query
     */
    scoreRuleRelevance(rules, query) {
        const queryLower = query.toLowerCase();
        const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
        return rules
            .map(rule => ({
            rule,
            score: this.calculateRelevanceScore(rule, queryWords, queryLower),
        }))
            .sort((a, b) => b.score - a.score);
    }
    /**
     * Calculate relevance score for a rule
     */
    calculateRelevanceScore(rule, queryWords, queryLower) {
        let score = 0;
        // Exact name match
        if (rule.name.toLowerCase().includes(queryLower)) {
            score += 100;
        }
        // Word matches in name
        for (const word of queryWords) {
            if (rule.name.toLowerCase().includes(word)) {
                score += 50;
            }
        }
        // Summary matches
        if (rule.summary) {
            const summaryLower = rule.summary.toLowerCase();
            for (const word of queryWords) {
                if (summaryLower.includes(word)) {
                    score += 20;
                }
            }
        }
        // Content matches (if available)
        if (rule.content) {
            const contentLower = rule.content.toLowerCase();
            for (const word of queryWords) {
                const matches = (contentLower.match(new RegExp(word, 'g')) || []).length;
                score += matches * 5;
            }
        }
        // Category bonus for specific categories
        if (queryWords.some(word => ['architecture', 'workflow', 'api', 'domain'].includes(word))) {
            score += 10;
        }
        return score;
    }
    /**
     * Create a structured rule system error
     */
    createRuleSystemError(type, message, context, originalError) {
        const error = new Error(message);
        error.type = type;
        error.context = context;
        error.originalError = originalError;
        return error;
    }
}
exports.ContextManager = ContextManager;
//# sourceMappingURL=contextManager.js.map