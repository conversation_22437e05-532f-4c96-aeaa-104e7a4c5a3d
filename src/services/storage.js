"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageService = exports.StorageKey = void 0;
const vscode = __importStar(require("vscode"));
var StorageKey;
(function (StorageKey) {
    StorageKey["RECENT_REPOSITORIES"] = "aidd.recentRepositories";
    StorageKey["LAST_REPOSITORY"] = "aidd.lastRepository";
})(StorageKey || (exports.StorageKey = StorageKey = {}));
class StorageService {
    context;
    constructor(context) {
        this.context = context;
    }
    getRecentRepositories() {
        const repos = this.context.globalState.get(StorageKey.RECENT_REPOSITORIES, []);
        return repos;
    }
    addRecentRepository(repository) {
        const repos = this.getRecentRepositories();
        const maxRecent = vscode.workspace
            .getConfiguration("aidd")
            .get("maxRecentRepositories") ?? 5;
        const filteredRepos = repos.filter((repo) => !(repo.owner === repository.owner && repo.name === repository.name));
        filteredRepos.unshift(repository);
        const limitedRepos = filteredRepos.slice(0, maxRecent);
        this.context.globalState.update(StorageKey.RECENT_REPOSITORIES, limitedRepos);
        this.setLastRepository(repository);
    }
    getLastRepository() {
        return this.context.globalState.get(StorageKey.LAST_REPOSITORY);
    }
    setLastRepository(repository) {
        this.context.globalState.update(StorageKey.LAST_REPOSITORY, repository);
    }
    clearStorage() {
        this.context.globalState.update(StorageKey.RECENT_REPOSITORIES, undefined);
        this.context.globalState.update(StorageKey.LAST_REPOSITORY, undefined);
    }
}
exports.StorageService = StorageService;
//# sourceMappingURL=storage.js.map