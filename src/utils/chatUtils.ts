import * as chatUtils from "@vscode/chat-extension-utils";
import * as vscode from "vscode";

export function registerChatLibChatParticipant(
  context: vscode.ExtensionContext
) {
  const handler: vscode.ChatRequestHandler = async (
    request: vscode.ChatRequest,
    chatContext: vscode.ChatContext,
    stream: vscode.ChatResponseStream,
    token: vscode.CancellationToken
  ) => {
    if (request.command === "list") {
      stream.markdown(
        `Available tools: ${vscode.lm.tools
          .map((tool) => tool.name)
          .join(", ")}\n\n`
      );
      return;
    }

    const tools =
      request.command === "all"
        ? vscode.lm.tools
        : vscode.lm.tools.filter((tool) =>
            tool.tags.includes("chat-tools-sample")
          );

    const libResult = chatUtils.sendChatParticipantRequest(
      request,
      chatContext,
      {
        prompt: "You are a cat! Answer as a cat.",
        responseStreamOptions: {
          stream,
          references: true,
          responseText: true,
        },
        tools,
      },
      token
    );

    return await libResult.result;
  };

  const chatLibParticipant = vscode.chat.createChatParticipant(
    "chat-tools-sample.catTools",
    handler
  );
  chatLibParticipant.iconPath = vscode.Uri.joinPath(
    context.extensionUri,
    "cat.jpeg"
  );
  context.subscriptions.push(chatLibParticipant);
}
