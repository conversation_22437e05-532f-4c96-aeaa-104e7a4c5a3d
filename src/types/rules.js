"use strict";
/**
 * Type definitions for the xendit-copilot rule system
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleSystemError = exports.CATEGORY_INFO = exports.RuleCategory = void 0;
/**
 * Category system for organizing rules
 */
var RuleCategory;
(function (RuleCategory) {
    RuleCategory[RuleCategory["ARCHITECTURE"] = 0] = "ARCHITECTURE";
    RuleCategory[RuleCategory["WORKFLOWS"] = 1] = "WORKFLOWS";
    RuleCategory[RuleCategory["OPENAPI"] = 2] = "OPENAPI";
    RuleCategory[RuleCategory["DOMAIN_RULES"] = 3] = "DOMAIN_RULES";
})(RuleCategory || (exports.RuleCategory = RuleCategory = {}));
/**
 * Category information mapping
 */
exports.CATEGORY_INFO = {
    [RuleCategory.ARCHITECTURE]: {
        number: 0,
        name: "Architecture",
        icon: "🏛️",
        purpose: "System design patterns",
        examples: ["Clean Architecture", "Onion", "3-tier"],
    },
    [RuleCategory.WORKFLOWS]: {
        number: 1,
        name: "Workflows",
        icon: "🔄",
        purpose: "Development processes",
        examples: ["PR reviews", "deployment", "CI/CD"],
    },
    [RuleCategory.OPENAPI]: {
        number: 2,
        name: "OpenAPI",
        icon: "🌐",
        purpose: "API specifications",
        examples: ["REST", "GraphQL", "gRPC guidelines"],
    },
    [RuleCategory.DOMAIN_RULES]: {
        number: 3,
        name: "Domain Rules",
        icon: "🎯",
        purpose: "Team/project-specific",
        examples: ["Custom business rules"],
    },
};
/**
 * Error types for rule system
 */
var RuleSystemError;
(function (RuleSystemError) {
    RuleSystemError["REPOSITORY_NOT_FOUND"] = "REPOSITORY_NOT_FOUND";
    RuleSystemError["MAIN_MDC_NOT_FOUND"] = "MAIN_MDC_NOT_FOUND";
    RuleSystemError["MAIN_MDC_PARSE_ERROR"] = "MAIN_MDC_PARSE_ERROR";
    RuleSystemError["RULE_FILE_NOT_FOUND"] = "RULE_FILE_NOT_FOUND";
    RuleSystemError["RULE_FILE_PARSE_ERROR"] = "RULE_FILE_PARSE_ERROR";
    RuleSystemError["INVALID_RULE_FILENAME"] = "INVALID_RULE_FILENAME";
    RuleSystemError["NETWORK_ERROR"] = "NETWORK_ERROR";
    RuleSystemError["PERMISSION_DENIED"] = "PERMISSION_DENIED";
})(RuleSystemError || (exports.RuleSystemError = RuleSystemError = {}));
//# sourceMappingURL=rules.js.map