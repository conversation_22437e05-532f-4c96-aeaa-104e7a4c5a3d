{"name": "sinon", "description": "JavaScript test spies, stubs and mocks.", "keywords": ["sinon", "test", "testing", "unit", "stub", "spy", "fake", "time", "clock", "mock", "xhr", "assert"], "version": "20.0.0", "homepage": "https://sinonjs.org/", "author": "<PERSON>", "repository": {"type": "git", "url": "http://github.com/sinonjs/sinon.git"}, "bugs": {"url": "http://github.com/sinonjs/sinon/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/sinon"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test-node": "mocha --recursive -R dot \"test/**/*-test.js\"", "test-dev": "npm run test-node --  -n watch -n  watch-path=test --node-option watch-path=lib -R min", "test-headless": "mochify --driver puppeteer", "test-coverage": "nyc nyc --exclude-after-remap false mochify --driver puppeteer --bundle 'node coverage.cjs'", "test-cloud": "./scripts/test-cloud.sh", "test-webworker": "mochify --driver puppeteer --serve . test/webworker/webworker-support-assessment.js", "test-esm-support": "mocha test/es2015/module-support-assessment-test.mjs", "test-esm-browser-build": "node test/es2015/check-esm-bundle-is-runnable.js", "test-runnable-examples": "docs/release-source/release/examples/run-test.sh", "test-docs": "cd docs; make check-links", "test": "npm run test-node && npm run test-headless && npm run test-webworker", "check-dependencies": "dependency-check package.json --no-dev --ignore-module esm", "update-compatibility": "node ./scripts/update-compatibility.cjs", "build": "node ./build.cjs", "build-docs": "cd docs; make build", "serve-docs": "cd docs; make livereload", "lint": "eslint --max-warnings 0 '**/*.{js,cjs,mjs}'", "pretest-webworker": "npm run build", "prebuild": "rimraf pkg && npm run check-dependencies && npm run update-compatibility", "postbuild": "npm run test-esm-support && npm run test-esm-browser-build", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "./scripts/preversion.sh", "version": "./scripts/version.sh", "postversion": "./scripts/postversion.sh"}, "nyc": {"instrument": false, "temp-dir": "coverage/.nyc_output", "reporter": ["text", "lcovonly"]}, "lint-staged": {"**/*.{js,css,md}": "prettier --write", "*.js": "eslint --quiet", "*.mjs": "eslint --quiet --ext mjs --parser-options=sourceType:module"}, "mochify": {"reporter": "dot", "timeout": 10000, "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\" --external:fs", "bundle_stdin": "require", "spec": "test/**/*-test.js"}, "dependencies": {"@sinonjs/commons": "^3.0.1", "@sinonjs/fake-timers": "^13.0.5", "@sinonjs/samsam": "^8.0.1", "diff": "^7.0.0", "supports-color": "^7.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@mochify/cli": "^0.4.1", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.1", "@sinonjs/referee": "^11.0.1", "@studio/changes": "^3.0.0", "babel-plugin-istanbul": "^7.0.0", "babelify": "^10.0.0", "browserify": "^16.5.2", "debug": "^4.3.7", "dependency-check": "^4.1.0", "esbuild": "^0.23.1", "esbuild-plugin-istanbul": "^0.3.0", "get-stdin": "^9.0.0", "lint-staged": "^15.2.10", "mocha": "^10.7.3", "nyc": "^17.0.0", "prettier": "^3.3.3", "puppeteer": "^23.3.0", "rimraf": "^6.0.1", "semver": "^7.6.3", "shelljs": "^0.8.5"}, "files": ["lib", "pkg", "scripts/support-sinon.js", "AUTHORS", "CONTRIBUTING.md", "CHANGELOG.md", "LICENSE", "README.md"], "browser": "./lib/sinon.js", "main": "./lib/sinon.js", "module": "./pkg/sinon-esm.js", "exports": {".": {"browser": "./pkg/sinon-esm.js", "require": "./lib/sinon.js", "import": "./pkg/sinon-esm.js"}, "./*": "./*"}, "type": "module", "cdn": "./pkg/sinon.js", "jsdelivr": "./pkg/sinon.js", "esm": {"cjs": {"mutableNamespace": false, "cache": true}, "mode": "auto"}}