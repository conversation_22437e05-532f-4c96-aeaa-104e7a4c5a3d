{"name": "mocha", "version": "11.5.0", "type": "commonjs", "description": "simple, flexible, fun test framework", "keywords": ["mocha", "test", "bdd", "tdd", "tap", "testing", "chai", "assertion", "ava", "jest", "tape", "jasmine", "karma"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mochajs/mocha.git"}, "bugs": {"url": "https://github.com/mochajs/mocha/issues/"}, "discord": "https://discord.gg/KeDn2uXhER", "homepage": "https://mochajs.org/", "logo": "https://cldup.com/S9uQ-cOLYz.svg", "notifyLogo": "https://ibin.co/4QuRuGjXvl36.png", "bin": {"mocha": "./bin/mocha.js", "_mocha": "./bin/_mocha"}, "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"build": "rollup -c ./rollup.config.js", "clean": "rimraf mocha.js mocha.js.map", "docs-clean": "rimraf docs/_site docs/api", "docs-watch": "eleventy --serve", "docs:api": "jsdoc -c jsdoc.conf.json", "docs:build": "eleventy", "docs:build-new": "cd docs-next && npm i && npm run build-with-old", "docs:preview": "http-server docs/_site -o", "docs": "run-s docs-clean docs:api docs:build docs:build-new", "format:eslint": "eslint --fix . \"bin/*\"", "format:prettier": "prettier --write \"!(package*).json\" \".*.json\" \"lib/**/*.json\" \"*.yml\"", "format": "run-s format:*", "lint:installed-check": "installed-check --engine-check", "lint:knip": "knip --cache", "lint:code": "eslint . \"bin/*\" --max-warnings 0", "lint:markdown": "markdownlint \"*.md\" \"docs/**/*.md\" \".github/*.md\" \"lib/**/*.md\" \"test/**/*.md\" \"example/**/*.md\" -i CHANGELOG.md", "lint": "run-p lint:*", "prepublishOnly": "run-s clean build", "test-browser-run": "cross-env NODE_PATH=. karma start ./karma.conf.js --single-run", "test-browser:reporters:bdd": "cross-env MOCHA_TEST=bdd npm run -s test-browser-run", "test-browser:reporters:esm": "cross-env MOCHA_TEST=esm npm run -s test-browser-run", "test-browser:reporters:qunit": "cross-env MOCHA_TEST=qunit npm run -s test-browser-run", "test-browser:reporters:tdd": "cross-env MOCHA_TEST=tdd npm run -s test-browser-run", "test-browser:reporters": "run-s test-browser:reporters:*", "test-browser:webpack-compat": "webpack --mode development --config ./test/browser-specific/fixtures/webpack/webpack.config.js", "test-browser": "run-s clean build test-browser:*", "test-coverage-clean": "rimraf .nyc_output coverage", "test-coverage-generate": "nyc report --reporter=lcov --reporter=text", "test-node-run-only": "nyc --no-clean --reporter=json node bin/mocha.js", "test-node-run": "nyc --no-clean --reporter=json node bin/mocha.js --forbid-only", "test-node:integration": "run-s clean build && npm run -s test-node-run -- --parallel --timeout 10000 --slow 3750 \"test/integration/**/*.spec.js\"", "test-node:interfaces:bdd": "npm run -s test-node-run -- --ui bdd test/interfaces/bdd.spec", "test-node:interfaces:exports": "npm run -s test-node-run -- --ui exports test/interfaces/exports.spec", "test-node:interfaces:qunit": "npm run -s test-node-run -- --ui qunit test/interfaces/qunit.spec", "test-node:interfaces:tdd": "npm run -s test-node-run -- --ui tdd test/interfaces/tdd.spec", "test-node:interfaces": "run-p test-node:interfaces:*", "test-node:jsapi": "node test/jsapi/index.js", "test-node:only:bddRequire": "npm run -s test-node-run-only -- --ui qunit test/only/bdd-require.spec --no-parallel", "test-node:only:globalBdd": "npm run -s test-node-run-only -- --ui bdd test/only/global/bdd.spec --no-parallel", "test-node:only:globalQunit": "npm run -s test-node-run-only -- --ui qunit test/only/global/qunit.spec --no-parallel", "test-node:only:globalTdd": "npm run -s test-node-run-only -- --ui tdd test/only/global/tdd.spec --no-parallel", "test-node:only": "run-p test-node:only:*", "test-node:reporters": "npm run -s test-node-run -- \"test/reporters/*.spec.js\"", "test-node:requires": "npm run -s test-node-run -- --require coffeescript/register --require test/require/a.js --require test/require/b.coffee --require test/require/c.js --require test/require/d.coffee test/require/require.spec.js", "test-node:unit": "npm run -s test-node-run -- \"test/unit/*.spec.js\" \"test/node-unit/**/*.spec.js\"", "test-node": "run-s test-coverage-clean test-node:* test-coverage-generate", "test-smoke": "node ./bin/mocha --no-config test/smoke/smoke.spec.js", "test": "run-s lint test-node test-browser", "version:linkify-changelog": "node scripts/linkify-changelog.mjs", "version:update-authors": "node scripts/update-authors.js", "version": "run-p version:* && git add -A ./AUTHORS ./CHANGELOG.md"}, "dependencies": {"browser-stdout": "^1.3.1", "chokidar": "^4.0.1", "debug": "^4.3.5", "diff": "^7.0.0", "escape-string-regexp": "^4.0.0", "find-up": "^5.0.0", "glob": "^10.4.5", "he": "^1.2.0", "js-yaml": "^4.1.0", "log-symbols": "^4.1.0", "minimatch": "^9.0.5", "ms": "^2.1.3", "picocolors": "^1.1.1", "serialize-javascript": "^6.0.2", "strip-json-comments": "^3.1.1", "supports-color": "^8.1.1", "workerpool": "^6.5.1", "yargs": "^17.7.2", "yargs-parser": "^21.1.1", "yargs-unparser": "^2.0.0"}, "devDependencies": {"@11ty/eleventy": "^1.0.0", "@11ty/eleventy-plugin-inclusive-language": "^1.0.3", "@eslint/js": "^8.56.0", "@mocha/docdash": "^4.0.1", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^21.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-multi-entry": "^4.0.1", "@rollup/plugin-node-resolve": "^13.1.3", "chai": "^4.3.4", "coffeescript": "^2.6.1", "cross-env": "^7.0.2", "eslint": "^8.56.0", "eslint-plugin-n": "^17.15.1", "fail-on-errors-webpack-plugin": "^3.0.0", "globals": "^13.24.0", "http-server": "^14.1.1", "installed-check": "^9.3.0", "jsdoc": "^3.6.7", "jsdoc-ts-utils": "^2.0.1", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^4.3.6", "knip": "^5.27.0", "markdown-it": "^12.3.2", "markdown-it-anchor": "^8.4.1", "markdown-it-attrs": "^4.1.3", "markdown-it-emoji": "^2.0.0", "markdown-it-prism": "^2.2.2", "markdown-toc": "^1.2.0", "markdownlint-cli": "^0.30.0", "needle": "^2.5.0", "npm-run-all2": "^6.2.0", "nyc": "^15.1.0", "pidtree": "^0.5.0", "prettier": "^2.4.1", "remark": "^14.0.2", "remark-github": "^11.2.2", "remark-inline-links": "^6.0.1", "rewiremock": "^3.14.3", "rimraf": "^3.0.2", "rollup": "^2.70.1", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-polyfill-node": "^0.8.0", "rollup-plugin-visualizer": "^5.6.0", "sinon": "^9.0.3", "unexpected": "^11.14.0", "unexpected-eventemitter": "^2.2.0", "unexpected-map": "^2.0.0", "unexpected-set": "^3.0.0", "unexpected-sinon": "^10.11.2", "uslug": "^1.0.4", "webpack": "^5.67.0", "webpack-cli": "^4.9.1"}, "files": ["bin/*mocha*", "lib/**/*.{js,html,json}", "index.js", "mocha.css", "mocha.js", "mocha.js.map", "browser-entry.js"], "browser": {"./index.js": "./browser-entry.js", "fs": false, "path": false, "supports-color": false, "./lib/nodejs/buffered-worker-pool.js": false, "./lib/nodejs/esm-utils.js": false, "./lib/nodejs/file-unloader.js": false, "./lib/nodejs/parallel-buffered-runner.js": false, "./lib/nodejs/serializer.js": false, "./lib/nodejs/worker.js": false, "./lib/nodejs/reporters/parallel-buffered.js": false, "./lib/cli/index.js": false}, "prettier": {"arrowParens": "avoid", "bracketSpacing": false, "endOfLine": "auto", "singleQuote": true, "trailingComma": "none"}, "overrides": {"webdriverio": "^7.33.0"}}