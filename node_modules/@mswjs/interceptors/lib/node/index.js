"use strict";Object.defineProperty(exports, "__esModule", {value: true});

var _chunkMCB574K6js = require('./chunk-MCB574K6.js');



var _chunkLK6DILFKjs = require('./chunk-LK6DILFK.js');


var _chunk73NOP3T5js = require('./chunk-73NOP3T5.js');








var _chunkDLID3GDGjs = require('./chunk-DLID3GDG.js');


var _chunkSMXZPJEAjs = require('./chunk-SMXZPJEA.js');

// src/utils/getCleanUrl.ts
function getCleanUrl(url, isAbsolute = true) {
  return [isAbsolute && url.origin, url.pathname].filter(Boolean).join("");
}














exports.BatchInterceptor = _chunkMCB574K6js.BatchInterceptor; exports.FetchResponse = _chunkDLID3GDGjs.FetchResponse; exports.INTERNAL_REQUEST_ID_HEADER_NAME = _chunkDLID3GDGjs.INTERNAL_REQUEST_ID_HEADER_NAME; exports.IS_PATCHED_MODULE = _chunk73NOP3T5js.IS_PATCHED_MODULE; exports.Interceptor = _chunkDLID3GDGjs.Interceptor; exports.InterceptorReadyState = _chunkDLID3GDGjs.InterceptorReadyState; exports.createRequestId = _chunkDLID3GDGjs.createRequestId; exports.decodeBuffer = _chunkLK6DILFKjs.decodeBuffer; exports.deleteGlobalSymbol = _chunkDLID3GDGjs.deleteGlobalSymbol; exports.encodeBuffer = _chunkLK6DILFKjs.encodeBuffer; exports.getCleanUrl = getCleanUrl; exports.getGlobalSymbol = _chunkDLID3GDGjs.getGlobalSymbol; exports.getRawRequest = _chunkSMXZPJEAjs.getRawRequest;
//# sourceMappingURL=index.js.map