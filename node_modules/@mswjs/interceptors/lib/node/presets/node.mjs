import {
  ClientRequestInterceptor
} from "../chunk-FHLAZ57F.mjs";
import "../chunk-TJDMZZXE.mjs";
import {
  XMLHttpRequestInterceptor
} from "../chunk-3HLZLASJ.mjs";
import "../chunk-6HYIRFX2.mjs";
import {
  FetchInterceptor
} from "../chunk-3TXENUZY.mjs";
import "../chunk-TX5GBTFY.mjs";
import "../chunk-6YM4PLBI.mjs";
import "../chunk-LGXJ3UUF.mjs";
import "../chunk-YM42IU6M.mjs";
import "../chunk-3GJB4JDF.mjs";

// src/presets/node.ts
var node_default = [
  new ClientRequestInterceptor(),
  new XMLHttpRequestInterceptor(),
  new FetchInterceptor()
];
export {
  node_default as default
};
//# sourceMappingURL=node.mjs.map