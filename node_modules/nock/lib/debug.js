'use strict'

const { debuglog } = require('util')

module.exports.back = debuglog('nock:back')
module.exports.common = debuglog('nock:common')
module.exports.intercept = debuglog('nock:intercept')
module.exports.request_overrider = debuglog('nock:request_overrider')
module.exports.playback_interceptor = debuglog('nock:playback_interceptor')
module.exports.recorder = debuglog('nock:recorder')
module.exports.socket = debuglog('nock:socket')
module.exports.scopeDebuglog = namespace => debuglog(`nock:scope:${namespace}`)
