{"name": "process-on-spawn", "version": "1.1.0", "description": "Execute callbacks when child processes are spawned", "scripts": {"release": "standard-version --sign", "pretest": "xo", "tests-only": "c8 --no-check-coverage -r none tape test/test.js | tap-min", "test": "npm run -s tests-only", "posttest": "if-ver -ge 10 || exit 0; c8 report"}, "engines": {"node": ">=8"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/cfware/process-on-spawn.git"}, "bugs": {"url": "https://github.com/cfware/process-on-spawn/issues"}, "homepage": "https://github.com/cfware/process-on-spawn#readme", "xo": {"rules": {"capitalized-comments": 0}}, "dependencies": {"fromentries": "^1.2.0"}, "devDependencies": {"@types/node": "^20.1.3", "c8": "^7.0.0", "if-ver": "^1.1.0", "standard-version": "^8.0.0", "tap-min": "^2.0.0", "tape": "^5.0.0", "xo": "^0.25.3"}}