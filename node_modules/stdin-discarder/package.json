{"name": "stdin-discarder", "version": "0.2.2", "description": "Discard stdin input except for Ctrl+C", "license": "MIT", "repository": "sindresorhus/stdin-discarder", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["stdin", "process", "standard", "discard", "ignore", "input"], "devDependencies": {"ava": "^6.0.1", "tsd": "^0.29.0", "xo": "^0.56.0"}}