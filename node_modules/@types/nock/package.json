{"name": "@types/nock", "version": "10.0.3", "description": "TypeScript definitions for nock", "license": "MIT", "contributors": [{"name": "bonnici", "url": "https://github.com/bonnici", "githubUsername": "bonnici"}, {"name": "<PERSON><PERSON><PERSON>_<PERSON>", "url": "https://github.com/horiuchi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/afharo", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/mastermatt", "githubUsername": "mastermatt"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/damour", "githubUsername": "damour"}, {"name": "GP", "url": "https://github.com/paambaati", "githubUsername": "paa<PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/nock"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "2538327aa64e9ea0fb0692a8dd6bdd77814ba2bb56b171a525622516b064bcfc", "typeScriptVersion": "2.0"}