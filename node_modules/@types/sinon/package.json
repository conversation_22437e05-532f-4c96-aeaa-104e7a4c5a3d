{"name": "@types/sinon", "version": "17.0.4", "description": "TypeScript definitions for sinon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sinon", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "mrbigdog2u", "url": "https://github.com/mrbigdog2u"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nicojs"}, {"name": "<PERSON>", "githubUsername": "43081j", "url": "https://github.com/43081j"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gje<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/johnjesse"}, {"name": "<PERSON>", "githubUsername": "alecf", "url": "https://github.com/alecf"}, {"name": "<PERSON>", "githubUsername": "Simon<PERSON><PERSON><PERSON>", "url": "https://github.com/SimonSchick"}, {"name": "<PERSON>", "githubUsername": "lo1tuma", "url": "https://github.com/lo1tuma"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sinon"}, "scripts": {}, "dependencies": {"@types/sinonjs__fake-timers": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d5cbbbf96c51c2cabba604f2bfb1fe0c26940bfe28cf3520fb40fee60a8201c0", "typeScriptVersion": "5.0"}