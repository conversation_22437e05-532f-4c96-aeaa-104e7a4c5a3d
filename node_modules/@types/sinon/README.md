# Installation
> `npm install --save @types/sinon`

# Summary
This package contains type definitions for sinon (https://sinonjs.org).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sinon.

### Additional Details
 * Last updated: Sat, 22 Feb 2025 12:42:31 GMT
 * Dependencies: [@types/sinonjs__fake-timers](https://npmjs.com/package/@types/sinonjs__fake-timers)

# Credits
These definitions were written by [<PERSON>](https://github.com/mrbigdog2u), [<PERSON>](https://github.com/nicojs), [<PERSON>](https://github.com/43081j), [<PERSON>](https://github.com/g<PERSON><PERSON><PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/johnjesse), [<PERSON>](https://github.com/alecf), [<PERSON>](https://github.com/SimonS<PERSON>ck), and [<PERSON>](https://github.com/lo1tuma).
