# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [3.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@3.0.0) (2019-12-20)

**Note:** Version bump only for package istanbul-lib-hook





# [3.0.0-alpha.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@3.0.0-alpha.2) (2019-12-07)

**Note:** Version bump only for package istanbul-lib-hook





# [3.0.0-alpha.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@3.0.0-alpha.1) (2019-10-06)


### Bug Fixes

* **package:** update append-transform to version 2.0.0 ([#466](https://github.com/istanbuljs/istanbuljs/issues/466)) ([7344b2b](https://github.com/istanbuljs/istanbuljs/commit/7344b2b))





# [3.0.0-alpha.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@3.0.0-alpha.0) (2019-06-19)


### Features

* Update dependencies, require Node.js 8 ([#401](https://github.com/istanbuljs/istanbuljs/issues/401)) ([bf3a539](https://github.com/istanbuljs/istanbuljs/commit/bf3a539))


### BREAKING CHANGES

* Node.js 8 is now required





## [2.0.7](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.7) (2019-04-24)

**Note:** Version bump only for package istanbul-lib-hook





## [2.0.6](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.6) (2019-04-09)

**Note:** Version bump only for package istanbul-lib-hook





## [2.0.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.5) (2019-04-03)

**Note:** Version bump only for package istanbul-lib-hook





## [2.0.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.4) (2019-03-12)

**Note:** Version bump only for package istanbul-lib-hook





## [2.0.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.3) (2019-01-26)

**Note:** Version bump only for package istanbul-lib-hook





<a name="2.0.2"></a>
## [2.0.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.2) (2018-12-25)




**Note:** Version bump only for package istanbul-lib-hook

<a name="2.0.1"></a>
## [2.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.1) (2018-07-07)




**Note:** Version bump only for package istanbul-lib-hook

<a name="2.0.0"></a>
# [2.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@2.0.0) (2018-06-06)


### Bug Fixes

* 1.2.x should have been a breaking change ([#181](https://github.com/istanbuljs/istanbuljs/issues/181)) ([2872835](https://github.com/istanbuljs/istanbuljs/commit/2872835))


### BREAKING CHANGES

* the closure provied to hookRequire, hookRunInThisContext, etc., is now passed an object with a filename member, rather than a string representing filename.




<a name="1.2.1"></a>
## [1.2.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@1.2.1) (2018-06-06)


### Bug Fixes

* pass correct args to tranformer ([#153](https://github.com/istanbuljs/istanbuljs/issues/153)) ([#154](https://github.com/istanbuljs/istanbuljs/issues/154)) ([2b2250f](https://github.com/istanbuljs/istanbuljs/commit/2b2250f))




<a name="1.2.0"></a>
# [1.2.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@1.2.0) (2018-03-04)


### Features

* hookRunInThisContext now takes options object rather than filename  ([#99](https://github.com/istanbuljs/istanbuljs/issues/99)) ([1504374](https://github.com/istanbuljs/istanbuljs/commit/1504374))




<a name="1.1.0"></a>
# [1.1.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@1.1.0) (2017-10-21)


### Features

* hook vm.runInContext ([#90](https://github.com/istanbuljs/istanbuljs/issues/90)) ([9659936](https://github.com/istanbuljs/istanbuljs/commit/9659936))




<a name="1.0.7"></a>
## [1.0.7](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-lib-hook@1.0.7) (2017-05-27)




<a name="1.0.6"></a>
## [1.0.6](https://github.com/istanbuljs/istanbul-lib-hook/compare/<EMAIL>-lib-hook@1.0.6) (2017-04-29)




<a name="1.0.5"></a>
## [1.0.5](https://github.com/istanbuljs/istanbul-lib-hook/compare/<EMAIL>-lib-hook@1.0.5) (2017-03-27)

<a name="1.0.4"></a>
## [1.0.4](https://github.com/istanbuljs/istanbul-lib-hook/compare/<EMAIL>-lib-hook@1.0.4) (2017-03-21)

<a name="1.0.3"></a>
## [1.0.3](https://github.com/istanbuljs/istanbul-lib-hook/compare/<EMAIL>-lib-hook@1.0.3) (2017-03-21)

<a name="1.0.2"></a>
## [1.0.2](https://github.com/istanbuljs/istanbul-lib-hook/compare/<EMAIL>-lib-hook@1.0.2) (2017-03-21)

<a name="1.0.0"></a>
# [1.0.0](https://github.com/istanbuljs/istanbul-lib-hook/compare/v1.0.0-alpha.3...v1.0.0) (2017-01-17)


### Bug Fixes

* update append-transform to version that fixes issues run into by ts-node ([f4aaf79](https://github.com/istanbuljs/istanbul-lib-hook/commit/f4aaf79))
