{"name": "foreground-child", "version": "2.0.0", "description": "Run a child as if it's the foreground process.  Give it stdio.  Exit when it exits.", "main": "index.js", "engines": {"node": ">=8.0.0"}, "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^3.0.2"}, "devDependencies": {"tap": "^14.6.1"}, "scripts": {"test": "tap", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "tap": {"jobs": 1}, "repository": {"type": "git", "url": "git+https://github.com/tapjs/foreground-child.git"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "bugs": {"url": "https://github.com/tapjs/foreground-child/issues"}, "homepage": "https://github.com/tapjs/foreground-child#readme", "directories": {"test": "test"}, "files": ["index.js"]}