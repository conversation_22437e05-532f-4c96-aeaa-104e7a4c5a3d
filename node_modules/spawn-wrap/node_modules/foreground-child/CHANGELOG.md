# Changes

## v2.0.0

* BREAKING CHANGE: Require Node 8
* Internal: Add lock file
* Support async before exit callback
* Update various dependencies

## v1.5.6

* Fix 'childHangup is undefined'

## v1.5.5

* add files list to package.json
* neveragain.tech pledge request

## v1.5.4

* update tap to v8
* Let the child decide if signals should be fatal

## v1.5.3

* bump deps

## v1.5.2

* add an automatic changelog script
* replace cross-spawn-async with cross-spawn
* test: stay alive long enough to be signaled

## v1.5.1

* avoid race condition in test
* Use fd numbers instead of 'inherit' for Node v0.10 compatibility

## v1.5.0

* add caveats re IPC and arbitrary FDs
* Forward IPC messages to foregrounded child process

## v1.4.0

* Set `process.exitCode` based on the child’s exit code

## v1.3.5

* Better testing for when cross-spawn-async needed
* appveyor: node v0.10 on windows is too flaky

## v1.3.4

* Only use cross-spawn-async for shebangs
* update vanity badges and package.json for repo move
* appveyor

## v1.3.3

* Skip signals in tests on Windows
* update to tap@4
* use cross-spawn-async on windows

## v1.3.2

* Revert "switch to win-spawn"
* Revert "Transparently translate high-order exit code to appropriate signal"
* update travis versions
* Transparently translate high-order exit code to appropriate signal
* ignore coverage folder

## v1.3.1

* switch to win-spawn

## v1.3.0

* note skipped test in test output
* left an unused var c in
* slice arguments, added documentation
* added a unit test, because I strive to be a good open-source-citizen
* make travis also work on 0.12 and iojs again
* added badge
* patch for travis exit weirdness
* fix typo in .gitignore
* beforeExit hook

## v1.2.0

* Use signal-exit, fix kill(process.pid) race

## v1.1.0

* Enforce that parent always gets a 'exit' event

## v1.0.0

* first
