{"name": "spawn-wrap", "version": "2.0.0", "description": "Wrap all spawned Node.js child processes by adding environs and arguments ahead of the main JavaScript file argument.", "main": "index.js", "engines": {"node": ">=8"}, "dependencies": {"foreground-child": "^2.0.0", "is-windows": "^1.0.2", "make-dir": "^3.0.0", "rimraf": "^3.0.0", "signal-exit": "^3.0.2", "which": "^2.0.1"}, "scripts": {"test": "tap", "release": "standard-version", "clean": "rm -rf ~/.node-spawn-wrap-*"}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/spawn-wrap.git"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "bugs": {"url": "https://github.com/istanbuljs/spawn-wrap/issues"}, "homepage": "https://github.com/istanbuljs/spawn-wrap#readme", "devDependencies": {"standard-version": "^7.1.0", "tap": "^14.10.5"}, "files": ["lib/", "index.js", "shim.js"], "tap": {"coverage": false, "timeout": 240}}