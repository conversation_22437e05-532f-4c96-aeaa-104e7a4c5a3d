# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [2.0.0](https://github.com/istanbuljs/spawn-wrap/compare/v1.4.3...v2.0.0) (2019-12-20)


### ⚠ BREAKING CHANGES

* no longer feature detect spawnSync, present since Node 0.11.
* Drop support for iojs (#84)
* explicitly drops support for Node 6

### Bug Fixes

* Avoid path concatenation ([5626f2a](https://github.com/istanbuljs/spawn-wrap/commit/5626f2a))
* Handle whitespace in homedir paths ([#98](https://github.com/istanbuljs/spawn-wrap/issues/98)) ([f002ecc](https://github.com/istanbuljs/spawn-wrap/commit/f002ecc)), closes [istanbuljs/nyc#784](https://github.com/istanbuljs/nyc/issues/784)
* Make munge functions pure ([#99](https://github.com/istanbuljs/spawn-wrap/issues/99)) ([5c1293e](https://github.com/istanbuljs/spawn-wrap/commit/5c1293e))
* Remove '/.node-spawn-wrap-' from lib/homedir.js export ([5bcb288](https://github.com/istanbuljs/spawn-wrap/commit/5bcb288))
* Remove legacy `ChildProcess` resolution ([#85](https://github.com/istanbuljs/spawn-wrap/issues/85)) ([da05012](https://github.com/istanbuljs/spawn-wrap/commit/da05012))
* Remove legacy `spawnSync` feature detection ([#87](https://github.com/istanbuljs/spawn-wrap/issues/87)) ([78777aa](https://github.com/istanbuljs/spawn-wrap/commit/78777aa))
* Switch from mkdirp to make-dir ([#94](https://github.com/istanbuljs/spawn-wrap/issues/94)) ([b8dace1](https://github.com/istanbuljs/spawn-wrap/commit/b8dace1))
* Use `is-windows` package for detection ([#88](https://github.com/istanbuljs/spawn-wrap/issues/88)) ([c3e6239](https://github.com/istanbuljs/spawn-wrap/commit/c3e6239)), closes [istanbuljs/spawn-wrap#61](https://github.com/istanbuljs/spawn-wrap/issues/61)
* Use safe path functions in `setup` ([#86](https://github.com/istanbuljs/spawn-wrap/issues/86)) ([4103f72](https://github.com/istanbuljs/spawn-wrap/commit/4103f72))

### Features

* Drop support for iojs ([#84](https://github.com/istanbuljs/spawn-wrap/issues/84)) ([6e86337](https://github.com/istanbuljs/spawn-wrap/commit/6e86337))
* require Node 8 ([#80](https://github.com/istanbuljs/spawn-wrap/issues/80)) ([19543e7](https://github.com/istanbuljs/spawn-wrap/commit/19543e7))


## [2.0.0-beta.0](https://github.com/istanbuljs/spawn-wrap/compare/v1.4.3...v2.0.0-beta.0) (2019-10-07)


See 2.0.0 for notes.


### [1.4.3](https://github.com/isaacs/spawn-wrap/compare/v1.4.2...v1.4.3) (2019-08-23)


### Bug Fixes

* **win32:** handle cases where "node" is quoted ([#102](https://github.com/isaacs/spawn-wrap/issues/102)) ([aac8730](https://github.com/isaacs/spawn-wrap/commit/aac8730))
