{"name": "release-zalgo", "version": "1.0.0", "description": "Helps you write code with promise-like chains that can run both synchronously and asynchronously", "main": "index.js", "files": ["index.js", "lib"], "engines": {"node": ">=4"}, "scripts": {"lint": "as-i-preach", "test": "ava", "posttest": "as-i-preach", "coverage": "nyc npm test"}, "repository": {"type": "git", "url": "git+https://github.com/novemberborn/release-zalgo.git"}, "keywords": ["babel"], "author": "<PERSON> (https://novemberborn.net/)", "license": "ISC", "bugs": {"url": "https://github.com/novemberborn/release-zalgo/issues"}, "homepage": "https://github.com/novemberborn/release-zalgo#readme", "dependencies": {"es6-error": "^4.0.1"}, "devDependencies": {"@novemberborn/as-i-preach": "^7.0.0", "ava": "^0.18.0", "coveralls": "^2.11.15", "nyc": "^10.1.2"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "standard-engine": "@novemberborn/as-i-preach"}