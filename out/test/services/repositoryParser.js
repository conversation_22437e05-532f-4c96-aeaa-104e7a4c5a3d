"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoryParser = void 0;
var rules_1 = require("../types/rules");
/**
 * Service for parsing repository structure and discovering rules
 */
var RepositoryParser = /** @class */ (function () {
    function RepositoryParser(logger, githubService, ruleParser) {
        this.logger = logger;
        this.githubService = githubService;
        this.ruleParser = ruleParser;
    }
    /**
     * Parse repository structure from main.mdc and discover rules
     */
    RepositoryParser.prototype.parseRepositoryStructure = function (owner, repo) {
        return __awaiter(this, void 0, void 0, function () {
            var structure, repository, fileResult, file, contentResult, error_1, _i, _a, _b, serviceName, serviceMapping, rules, error_2, error_3;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        _c.trys.push([0, 13, , 14]);
                        this.logger.info("Parsing repository structure for ".concat(owner, "/").concat(repo));
                        structure = void 0;
                        _c.label = 1;
                    case 1:
                        _c.trys.push([1, 4, , 6]);
                        repository = { owner: owner, name: repo };
                        return [4 /*yield*/, this.githubService.fetchRepositoryContent(repository, 'main.mdc')];
                    case 2:
                        fileResult = _c.sent();
                        if (!fileResult.success || fileResult.data.length === 0) {
                            throw new Error('main.mdc not found');
                        }
                        file = fileResult.data[0];
                        if (!file.download_url) {
                            throw new Error('No download URL available for main.mdc');
                        }
                        return [4 /*yield*/, this.githubService.fetchFileContent(file.download_url)];
                    case 3:
                        contentResult = _c.sent();
                        if (!contentResult.success) {
                            throw contentResult.error;
                        }
                        structure = this.parseMainMdc(contentResult.data);
                        return [3 /*break*/, 6];
                    case 4:
                        error_1 = _c.sent();
                        this.logger.warn("main.mdc not found, discovering structure automatically");
                        return [4 /*yield*/, this.discoverRepositoryStructure(owner, repo)];
                    case 5:
                        structure = _c.sent();
                        return [3 /*break*/, 6];
                    case 6:
                        _i = 0, _a = Object.entries(structure.services);
                        _c.label = 7;
                    case 7:
                        if (!(_i < _a.length)) return [3 /*break*/, 12];
                        _b = _a[_i], serviceName = _b[0], serviceMapping = _b[1];
                        _c.label = 8;
                    case 8:
                        _c.trys.push([8, 10, , 11]);
                        return [4 /*yield*/, this.discoverRuleFiles(owner, repo, serviceName)];
                    case 9:
                        rules = _c.sent();
                        this.updateServiceMappingWithRules(serviceMapping, rules);
                        return [3 /*break*/, 11];
                    case 10:
                        error_2 = _c.sent();
                        this.logger.warn("Failed to discover rules for service ".concat(serviceName, ": ").concat(error_2));
                        return [3 /*break*/, 11];
                    case 11:
                        _i++;
                        return [3 /*break*/, 7];
                    case 12:
                        // Update total rule count
                        structure.totalRules = Object.values(structure.services)
                            .reduce(function (total, service) { return total + Object.values(service.ruleCounts)
                            .reduce(function (sum, count) { return sum + count; }, 0); }, 0);
                        structure.lastParsed = new Date();
                        structure.repositoryUrl = "https://github.com/".concat(owner, "/").concat(repo);
                        this.logger.info("Repository structure parsed successfully. Found ".concat(structure.totalRules, " rules across ").concat(Object.keys(structure.services).length, " services"));
                        return [2 /*return*/, structure];
                    case 13:
                        error_3 = _c.sent();
                        this.logger.error("Error parsing repository structure for ".concat(owner, "/").concat(repo, ":"), error_3);
                        throw this.createRuleSystemError(rules_1.RuleSystemError.MAIN_MDC_PARSE_ERROR, "Failed to parse repository structure: ".concat(owner, "/").concat(repo), { owner: owner, repo: repo }, error_3);
                    case 14: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Discover rule files in a service directory
     */
    RepositoryParser.prototype.discoverRuleFiles = function (owner, repo, servicePath) {
        return __awaiter(this, void 0, void 0, function () {
            var repository, result, files, rules, _i, files_1, file, metadata, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        this.logger.debug("Discovering rule files in ".concat(servicePath));
                        repository = { owner: owner, name: repo };
                        return [4 /*yield*/, this.githubService.fetchRepositoryContent(repository, servicePath)];
                    case 1:
                        result = _a.sent();
                        if (!result.success) {
                            throw result.error;
                        }
                        files = result.data;
                        rules = [];
                        for (_i = 0, files_1 = files; _i < files_1.length; _i++) {
                            file = files_1[_i];
                            if (file.type === 'file' && file.name.endsWith('.mdc') && file.name !== 'main.mdc') {
                                metadata = this.ruleParser.parseRuleFilename(file.name, servicePath, file.path);
                                if (metadata) {
                                    rules.push(metadata);
                                }
                            }
                        }
                        this.logger.debug("Found ".concat(rules.length, " rule files in ").concat(servicePath));
                        return [2 /*return*/, rules];
                    case 2:
                        error_4 = _a.sent();
                        this.logger.error("Error discovering rule files in ".concat(servicePath, ":"), error_4);
                        throw this.createRuleSystemError(rules_1.RuleSystemError.RULE_FILE_NOT_FOUND, "Failed to discover rule files in service: ".concat(servicePath), { servicePath: servicePath }, error_4);
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Parse main.mdc file content
     */
    RepositoryParser.prototype.parseMainMdc = function (content) {
        var _a;
        try {
            this.logger.debug('Parsing main.mdc content');
            var structure = {
                services: {},
                totalRules: 0,
                lastParsed: new Date(),
            };
            // Extract description from first paragraph
            var lines = content.split('\n');
            var descriptionLines = [];
            var inDescription = true;
            for (var _i = 0, lines_1 = lines; _i < lines_1.length; _i++) {
                var line = lines_1[_i];
                var trimmedLine = line.trim();
                if (!trimmedLine && inDescription) {
                    break;
                }
                if (trimmedLine && !trimmedLine.startsWith('#') && inDescription) {
                    descriptionLines.push(trimmedLine);
                }
                else if (trimmedLine.startsWith('#')) {
                    inDescription = false;
                }
            }
            if (descriptionLines.length > 0) {
                structure.description = descriptionLines.join(' ').trim();
            }
            // Parse service mappings (simplified - could be enhanced with more sophisticated parsing)
            var servicePattern = /##\s+([^#\n]+)/g;
            var match = void 0;
            while ((match = servicePattern.exec(content)) !== null) {
                var serviceName = match[1].trim().toLowerCase().replace(/\s+/g, '-');
                structure.services[serviceName] = {
                    name: match[1].trim(),
                    categories: [rules_1.RuleCategory.ARCHITECTURE, rules_1.RuleCategory.WORKFLOWS, rules_1.RuleCategory.OPENAPI, rules_1.RuleCategory.DOMAIN_RULES],
                    ruleCounts: (_a = {},
                        _a[rules_1.RuleCategory.ARCHITECTURE] = 0,
                        _a[rules_1.RuleCategory.WORKFLOWS] = 0,
                        _a[rules_1.RuleCategory.OPENAPI] = 0,
                        _a[rules_1.RuleCategory.DOMAIN_RULES] = 0,
                        _a),
                };
            }
            this.logger.debug("Parsed main.mdc: found ".concat(Object.keys(structure.services).length, " services"));
            return structure;
        }
        catch (error) {
            this.logger.error('Error parsing main.mdc content:', error);
            throw this.createRuleSystemError(rules_1.RuleSystemError.MAIN_MDC_PARSE_ERROR, 'Failed to parse main.mdc content', { contentLength: content.length }, error);
        }
    };
    /**
     * Validate repository structure
     */
    RepositoryParser.prototype.validateRepositoryStructure = function (structure) {
        try {
            if (!structure.services || Object.keys(structure.services).length === 0) {
                return false;
            }
            for (var _i = 0, _a = Object.entries(structure.services); _i < _a.length; _i++) {
                var _b = _a[_i], serviceName = _b[0], service = _b[1];
                if (!service.name || !service.categories || !service.ruleCounts) {
                    this.logger.warn("Invalid service structure for ".concat(serviceName));
                    return false;
                }
            }
            return true;
        }
        catch (error) {
            this.logger.error('Error validating repository structure:', error);
            return false;
        }
    };
    /**
     * Discover repository structure automatically when main.mdc is not available
     */
    RepositoryParser.prototype.discoverRepositoryStructure = function (owner, repo) {
        return __awaiter(this, void 0, void 0, function () {
            var structure, repository, result, rootContents, _i, rootContents_1, item, error_5;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.logger.info('Discovering repository structure automatically');
                        structure = {
                            description: "Auto-discovered structure for ".concat(owner, "/").concat(repo),
                            services: {},
                            totalRules: 0,
                            lastParsed: new Date(),
                        };
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 3, , 4]);
                        repository = { owner: owner, name: repo };
                        return [4 /*yield*/, this.githubService.fetchRepositoryContent(repository, '')];
                    case 2:
                        result = _b.sent();
                        if (result.success) {
                            rootContents = result.data;
                            for (_i = 0, rootContents_1 = rootContents; _i < rootContents_1.length; _i++) {
                                item = rootContents_1[_i];
                                if (item.type === 'dir' && !item.name.startsWith('.')) {
                                    structure.services[item.name] = {
                                        name: item.name,
                                        categories: [rules_1.RuleCategory.ARCHITECTURE, rules_1.RuleCategory.WORKFLOWS, rules_1.RuleCategory.OPENAPI, rules_1.RuleCategory.DOMAIN_RULES],
                                        ruleCounts: (_a = {},
                                            _a[rules_1.RuleCategory.ARCHITECTURE] = 0,
                                            _a[rules_1.RuleCategory.WORKFLOWS] = 0,
                                            _a[rules_1.RuleCategory.OPENAPI] = 0,
                                            _a[rules_1.RuleCategory.DOMAIN_RULES] = 0,
                                            _a),
                                    };
                                }
                            }
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        error_5 = _b.sent();
                        this.logger.warn("Failed to auto-discover repository structure: ".concat(error_5));
                        return [3 /*break*/, 4];
                    case 4: return [2 /*return*/, structure];
                }
            });
        });
    };
    /**
     * Update service mapping with discovered rules
     */
    RepositoryParser.prototype.updateServiceMappingWithRules = function (serviceMapping, rules) {
        var _a;
        // Reset counts
        serviceMapping.ruleCounts = (_a = {},
            _a[rules_1.RuleCategory.ARCHITECTURE] = 0,
            _a[rules_1.RuleCategory.WORKFLOWS] = 0,
            _a[rules_1.RuleCategory.OPENAPI] = 0,
            _a[rules_1.RuleCategory.DOMAIN_RULES] = 0,
            _a);
        // Count rules by category
        for (var _i = 0, rules_2 = rules; _i < rules_2.length; _i++) {
            var rule = rules_2[_i];
            serviceMapping.ruleCounts[rule.category]++;
        }
        // Update available categories based on actual rules
        serviceMapping.categories = Object.entries(serviceMapping.ruleCounts)
            .filter(function (_a) {
            var count = _a[1];
            return count > 0;
        })
            .map(function (_a) {
            var category = _a[0];
            return parseInt(category, 10);
        });
    };
    /**
     * Create a structured rule system error
     */
    RepositoryParser.prototype.createRuleSystemError = function (type, message, context, originalError) {
        var error = new Error(message);
        error.type = type;
        error.context = context;
        error.originalError = originalError;
        return error;
    };
    return RepositoryParser;
}());
exports.RepositoryParser = RepositoryParser;
