"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
var vscode = require("vscode");
var Logger = /** @class */ (function () {
    function Logger(channelName, debugMode) {
        if (debugMode === void 0) { debugMode = false; }
        this.outputChannel = vscode.window.createOutputChannel(channelName);
        this.debugMode = debugMode;
    }
    Logger.prototype.getTimestamp = function () {
        return "[".concat(new Date().toLocaleTimeString(), "]");
    };
    Logger.prototype.log = function (message) {
        this.outputChannel.appendLine("".concat(this.getTimestamp(), " ").concat(message));
    };
    Logger.prototype.error = function (message, error) {
        this.outputChannel.appendLine("".concat(this.getTimestamp(), " ERROR: ").concat(message));
        if (error) {
            if (error instanceof Error) {
                this.outputChannel.appendLine("".concat(error.message));
                if (error.stack) {
                    this.outputChannel.appendLine(error.stack);
                }
            }
            else if (typeof error === "object" && error !== null) {
                this.outputChannel.appendLine(JSON.stringify(error, null, 2));
            }
            else {
                this.outputChannel.appendLine(String(error));
            }
        }
    };
    Logger.prototype.info = function (message) {
        this.outputChannel.appendLine("".concat(this.getTimestamp(), " INFO: ").concat(message));
    };
    Logger.prototype.warn = function (message) {
        this.outputChannel.appendLine("".concat(this.getTimestamp(), " WARNING: ").concat(message));
    };
    Logger.prototype.debug = function (message) {
        if (this.debugMode) {
            this.outputChannel.appendLine("".concat(this.getTimestamp(), " DEBUG: ").concat(message));
        }
    };
    Logger.prototype.show = function () {
        this.outputChannel.show();
    };
    Logger.prototype.dispose = function () {
        this.outputChannel.dispose();
    };
    return Logger;
}());
exports.Logger = Logger;
