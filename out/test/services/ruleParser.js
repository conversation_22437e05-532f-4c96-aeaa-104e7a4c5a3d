"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleParser = void 0;
var rules_1 = require("../types/rules");
/**
 * Service for parsing rule files and extracting metadata
 */
var RuleParser = /** @class */ (function () {
    function RuleParser(logger) {
        // Rule filename pattern: ##-rule-name[@version][-specificity].mdc
        this.RULE_FILENAME_PATTERN = /^(\d{2})-([^@\-]+)(?:@([^-]+))?(?:-([^.]+))?\.mdc$/;
        this.logger = logger;
    }
    /**
     * Parse rule filename to extract metadata
     */
    RuleParser.prototype.parseRuleFilename = function (filename, service, path) {
        try {
            var match = this.RULE_FILENAME_PATTERN.exec(filename);
            if (!match) {
                this.logger.warn("Invalid rule filename format: ".concat(filename));
                return null;
            }
            var categoryStr = match[1], name_1 = match[2], version = match[3], specificity = match[4];
            var categoryNum = parseInt(categoryStr, 10);
            // Validate category number
            if (categoryNum < 0 || categoryNum > 3) {
                this.logger.warn("Invalid category number in filename: ".concat(filename));
                return null;
            }
            var category = categoryNum;
            var metadata = {
                filename: filename,
                category: category,
                name: this.cleanRuleName(name_1),
                path: path,
                service: service,
            };
            if (version) {
                metadata.version = version;
            }
            if (specificity) {
                metadata.specificity = specificity;
            }
            this.logger.debug("Parsed rule metadata for ".concat(filename, ": ").concat(JSON.stringify(metadata)));
            return metadata;
        }
        catch (error) {
            this.logger.error("Error parsing rule filename ".concat(filename, ":"), error);
            return null;
        }
    };
    /**
     * Parse rule content from markdown
     */
    RuleParser.prototype.parseRuleContent = function (metadata, content) {
        try {
            var summary = this.extractSummary(content);
            var ruleContent = __assign(__assign({}, metadata), { content: content.trim(), summary: summary, lastModified: new Date() });
            this.logger.debug("Parsed rule content for ".concat(metadata.filename, ", length: ").concat(content.length));
            return ruleContent;
        }
        catch (error) {
            this.logger.error("Error parsing rule content for ".concat(metadata.filename, ":"), error);
            throw this.createRuleSystemError(rules_1.RuleSystemError.RULE_FILE_PARSE_ERROR, "Failed to parse rule content: ".concat(metadata.filename), { metadata: metadata }, error);
        }
    };
    /**
     * Validate rule filename format
     */
    RuleParser.prototype.validateRuleFilename = function (filename) {
        return this.RULE_FILENAME_PATTERN.test(filename);
    };
    /**
     * Extract summary from rule content
     */
    RuleParser.prototype.extractSummary = function (content) {
        try {
            // Remove markdown headers and formatting
            var cleanContent = content
                .replace(/^#+\s*/gm, '') // Remove headers
                .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
                .replace(/\*(.*?)\*/g, '$1') // Remove italic
                .replace(/`(.*?)`/g, '$1') // Remove inline code
                .replace(/```[\s\S]*?```/g, '') // Remove code blocks
                .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
                .trim();
            // Get first paragraph or first 200 characters
            var firstParagraph = cleanContent.split('\n\n')[0];
            var summary = firstParagraph.length > 200
                ? firstParagraph.substring(0, 200) + '...'
                : firstParagraph;
            return summary || 'No summary available';
        }
        catch (error) {
            this.logger.warn("Error extracting summary from content: ".concat(error));
            return 'Summary extraction failed';
        }
    };
    /**
     * Clean rule name by replacing hyphens with spaces and capitalizing
     */
    RuleParser.prototype.cleanRuleName = function (name) {
        return name
            .replace(/-/g, ' ')
            .replace(/\b\w/g, function (char) { return char.toUpperCase(); });
    };
    /**
     * Create a structured rule system error
     */
    RuleParser.prototype.createRuleSystemError = function (type, message, context, originalError) {
        var error = new Error(message);
        error.type = type;
        error.context = context;
        error.originalError = originalError;
        return error;
    };
    /**
     * Parse category number to RuleCategory enum
     */
    RuleParser.parseCategoryNumber = function (categoryNum) {
        if (categoryNum >= 0 && categoryNum <= 3) {
            return categoryNum;
        }
        return null;
    };
    /**
     * Get category display name
     */
    RuleParser.getCategoryDisplayName = function (category) {
        var _a;
        var names = (_a = {},
            _a[rules_1.RuleCategory.ARCHITECTURE] = 'Architecture',
            _a[rules_1.RuleCategory.WORKFLOWS] = 'Workflows',
            _a[rules_1.RuleCategory.OPENAPI] = 'OpenAPI',
            _a[rules_1.RuleCategory.DOMAIN_RULES] = 'Domain Rules',
            _a);
        return names[category];
    };
    /**
     * Format rule metadata for display
     */
    RuleParser.formatRuleMetadata = function (metadata) {
        var parts = [
            "**".concat(metadata.name, "**"),
            "Category: ".concat(RuleParser.getCategoryDisplayName(metadata.category)),
            "Service: ".concat(metadata.service),
        ];
        if (metadata.version) {
            parts.push("Version: ".concat(metadata.version));
        }
        if (metadata.specificity) {
            parts.push("Scope: ".concat(metadata.specificity));
        }
        parts.push("File: `".concat(metadata.filename, "`"));
        return parts.join(' | ');
    };
    return RuleParser;
}());
exports.RuleParser = RuleParser;
