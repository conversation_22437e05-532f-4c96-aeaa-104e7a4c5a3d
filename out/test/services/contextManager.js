"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextManager = void 0;
var rules_1 = require("../types/rules");
var repositoryParser_1 = require("./repositoryParser");
var ruleParser_1 = require("./ruleParser");
/**
 * Service for managing context retrieval and session persistence
 */
var ContextManager = /** @class */ (function () {
    function ContextManager(logger, githubService, storageService) {
        // Cache for repository structure and rule content
        this.repositoryStructureCache = new Map();
        this.ruleContentCache = new Map();
        this.sessions = new Map();
        this.logger = logger;
        this.githubService = githubService;
        this.storageService = storageService;
        this.ruleParser = new ruleParser_1.RuleParser(logger);
        this.repositoryParser = new repositoryParser_1.RepositoryParser(logger, githubService, this.ruleParser);
    }
    /**
     * Retrieve relevant context based on user query
     */
    ContextManager.prototype.retrieveContext = function (params) {
        return __awaiter(this, void 0, void 0, function () {
            var repositoryStructure, criteria, allRules, scoredRules, maxRules, limitedRules, rules, _a, result, error_1;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 6, , 7]);
                        this.logger.info("Retrieving context for query: \"".concat(params.query, "\""));
                        return [4 /*yield*/, this.getRepositoryStructure()];
                    case 1:
                        repositoryStructure = _b.sent();
                        if (!repositoryStructure) {
                            throw this.createRuleSystemError(rules_1.RuleSystemError.REPOSITORY_NOT_FOUND, 'No repository structure available');
                        }
                        criteria = this.parseQueryToCriteria(params);
                        return [4 /*yield*/, this.searchRules(criteria)];
                    case 2:
                        allRules = _b.sent();
                        scoredRules = this.scoreRuleRelevance(allRules, params.query);
                        maxRules = params.maxRules || 5;
                        limitedRules = scoredRules.slice(0, maxRules);
                        if (!(params.includeContent !== false)) return [3 /*break*/, 4];
                        return [4 /*yield*/, Promise.all(limitedRules.map(function (_a) {
                                var rule = _a.rule;
                                return _this.getRuleContent(rule);
                            }))];
                    case 3:
                        _a = _b.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        _a = limitedRules.map(function (_a) {
                            var rule = _a.rule;
                            return rule;
                        });
                        _b.label = 5;
                    case 5:
                        rules = _a;
                        result = {
                            rules: rules,
                            totalFound: allRules.length,
                            query: params.query,
                            scores: limitedRules.map(function (_a) {
                                var score = _a.score;
                                return score;
                            }),
                            repositoryStructure: repositoryStructure,
                        };
                        this.logger.info("Retrieved ".concat(rules.length, " rules out of ").concat(allRules.length, " found"));
                        return [2 /*return*/, result];
                    case 6:
                        error_1 = _b.sent();
                        this.logger.error('Error retrieving context:', error_1);
                        throw error_1;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get repository structure for current repository
     */
    ContextManager.prototype.getRepositoryStructure = function () {
        return __awaiter(this, void 0, void 0, function () {
            var lastRepo, cacheKey, cached, structure, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        lastRepo = this.storageService.getLastRepository();
                        if (!lastRepo) {
                            this.logger.warn('No repository selected');
                            return [2 /*return*/, null];
                        }
                        cacheKey = "".concat(lastRepo.owner, "/").concat(lastRepo.name);
                        // Check cache first
                        if (this.repositoryStructureCache.has(cacheKey)) {
                            cached = this.repositoryStructureCache.get(cacheKey);
                            // Check if cache is still valid (1 hour)
                            if (Date.now() - cached.lastParsed.getTime() < 3600000) {
                                return [2 /*return*/, cached];
                            }
                        }
                        return [4 /*yield*/, this.repositoryParser.parseRepositoryStructure(lastRepo.owner, lastRepo.name)];
                    case 1:
                        structure = _a.sent();
                        // Cache the result
                        this.repositoryStructureCache.set(cacheKey, structure);
                        return [2 /*return*/, structure];
                    case 2:
                        error_2 = _a.sent();
                        this.logger.error('Error getting repository structure:', error_2);
                        return [2 /*return*/, null];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Search rules based on criteria
     */
    ContextManager.prototype.searchRules = function (criteria) {
        return __awaiter(this, void 0, void 0, function () {
            var repositoryStructure, rules, servicesToSearch, _i, servicesToSearch_1, serviceName, service, lastRepo, serviceRules, _a, serviceRules_1, ruleMetadata, ruleContent, error_3, error_4;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 12, , 13]);
                        return [4 /*yield*/, this.getRepositoryStructure()];
                    case 1:
                        repositoryStructure = _b.sent();
                        if (!repositoryStructure) {
                            return [2 /*return*/, []];
                        }
                        rules = [];
                        servicesToSearch = criteria.service
                            ? [criteria.service]
                            : Object.keys(repositoryStructure.services);
                        _i = 0, servicesToSearch_1 = servicesToSearch;
                        _b.label = 2;
                    case 2:
                        if (!(_i < servicesToSearch_1.length)) return [3 /*break*/, 11];
                        serviceName = servicesToSearch_1[_i];
                        service = repositoryStructure.services[serviceName];
                        if (!service)
                            return [3 /*break*/, 10];
                        _b.label = 3;
                    case 3:
                        _b.trys.push([3, 9, , 10]);
                        lastRepo = this.storageService.getLastRepository();
                        return [4 /*yield*/, this.repositoryParser.discoverRuleFiles(lastRepo.owner, lastRepo.name, serviceName)];
                    case 4:
                        serviceRules = _b.sent();
                        _a = 0, serviceRules_1 = serviceRules;
                        _b.label = 5;
                    case 5:
                        if (!(_a < serviceRules_1.length)) return [3 /*break*/, 8];
                        ruleMetadata = serviceRules_1[_a];
                        if (!this.matchesSearchCriteria(ruleMetadata, criteria)) return [3 /*break*/, 7];
                        return [4 /*yield*/, this.getRuleContent(ruleMetadata)];
                    case 6:
                        ruleContent = _b.sent();
                        rules.push(ruleContent);
                        _b.label = 7;
                    case 7:
                        _a++;
                        return [3 /*break*/, 5];
                    case 8: return [3 /*break*/, 10];
                    case 9:
                        error_3 = _b.sent();
                        this.logger.warn("Error searching rules in service ".concat(serviceName, ": ").concat(error_3));
                        return [3 /*break*/, 10];
                    case 10:
                        _i++;
                        return [3 /*break*/, 2];
                    case 11: return [2 /*return*/, rules];
                    case 12:
                        error_4 = _b.sent();
                        this.logger.error('Error searching rules:', error_4);
                        return [2 /*return*/, []];
                    case 13: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get rule content by metadata
     */
    ContextManager.prototype.getRuleContent = function (metadata) {
        return __awaiter(this, void 0, void 0, function () {
            var cacheKey, lastRepo, fileResult, file, contentResult, content, ruleContent, error_5;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        cacheKey = "".concat(metadata.service, "/").concat(metadata.filename);
                        // Check cache first
                        if (this.ruleContentCache.has(cacheKey)) {
                            return [2 /*return*/, this.ruleContentCache.get(cacheKey)];
                        }
                        lastRepo = this.storageService.getLastRepository();
                        return [4 /*yield*/, this.githubService.fetchRepositoryContent(lastRepo, metadata.path)];
                    case 1:
                        fileResult = _a.sent();
                        if (!fileResult.success || fileResult.data.length === 0) {
                            throw new Error("File not found: ".concat(metadata.path));
                        }
                        file = fileResult.data[0];
                        if (!file.download_url) {
                            throw new Error("No download URL available for file: ".concat(metadata.path));
                        }
                        return [4 /*yield*/, this.githubService.fetchFileContent(file.download_url)];
                    case 2:
                        contentResult = _a.sent();
                        if (!contentResult.success) {
                            throw contentResult.error;
                        }
                        content = contentResult.data;
                        ruleContent = this.ruleParser.parseRuleContent(metadata, content);
                        // Cache the result
                        this.ruleContentCache.set(cacheKey, ruleContent);
                        return [2 /*return*/, ruleContent];
                    case 3:
                        error_5 = _a.sent();
                        this.logger.error("Error getting rule content for ".concat(metadata.filename, ":"), error_5);
                        throw this.createRuleSystemError(rules_1.RuleSystemError.RULE_FILE_NOT_FOUND, "Failed to get rule content: ".concat(metadata.filename), { metadata: metadata }, error_5);
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Clear cached data
     */
    ContextManager.prototype.clearCache = function () {
        this.repositoryStructureCache.clear();
        this.ruleContentCache.clear();
        this.logger.info('Context manager cache cleared');
    };
    /**
     * Initialize context session
     */
    ContextManager.prototype.initializeSession = function () {
        var sessionId = "session_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
        var session = {
            id: sessionId,
            retrievedRules: new Set(),
            startTime: new Date(),
            lastActivity: new Date(),
        };
        this.sessions.set(sessionId, session);
        this.logger.debug("Initialized context session: ".concat(sessionId));
        return sessionId;
    };
    /**
     * Get context session
     */
    ContextManager.prototype.getSession = function (sessionId) {
        return this.sessions.get(sessionId) || null;
    };
    /**
     * Parse user query to search criteria
     */
    ContextManager.prototype.parseQueryToCriteria = function (params) {
        var keywords = params.query
            .toLowerCase()
            .split(/\s+/)
            .filter(function (word) { return word.length > 2; });
        return {
            keywords: keywords,
            category: params.category,
            service: params.service,
            searchContent: true,
        };
    };
    /**
     * Check if rule metadata matches search criteria
     */
    ContextManager.prototype.matchesSearchCriteria = function (metadata, criteria) {
        // Category filter
        if (criteria.category !== undefined && metadata.category !== criteria.category) {
            return false;
        }
        // Service filter
        if (criteria.service && metadata.service !== criteria.service) {
            return false;
        }
        // Version filter
        if (criteria.version && metadata.version !== criteria.version) {
            return false;
        }
        // Specificity filter
        if (criteria.specificity && metadata.specificity !== criteria.specificity) {
            return false;
        }
        // Keyword matching
        if (criteria.keywords.length > 0) {
            var searchText_1 = "".concat(metadata.name, " ").concat(metadata.filename).toLowerCase();
            var hasMatch = criteria.keywords.some(function (keyword) { return searchText_1.includes(keyword); });
            if (!hasMatch) {
                return false;
            }
        }
        return true;
    };
    /**
     * Score rule relevance based on query
     */
    ContextManager.prototype.scoreRuleRelevance = function (rules, query) {
        var _this = this;
        var queryLower = query.toLowerCase();
        var queryWords = queryLower.split(/\s+/).filter(function (word) { return word.length > 2; });
        return rules
            .map(function (rule) { return ({
            rule: rule,
            score: _this.calculateRelevanceScore(rule, queryWords, queryLower),
        }); })
            .sort(function (a, b) { return b.score - a.score; });
    };
    /**
     * Calculate relevance score for a rule
     */
    ContextManager.prototype.calculateRelevanceScore = function (rule, queryWords, queryLower) {
        var score = 0;
        // Exact name match
        if (rule.name.toLowerCase().includes(queryLower)) {
            score += 100;
        }
        // Word matches in name
        for (var _i = 0, queryWords_1 = queryWords; _i < queryWords_1.length; _i++) {
            var word = queryWords_1[_i];
            if (rule.name.toLowerCase().includes(word)) {
                score += 50;
            }
        }
        // Summary matches
        if (rule.summary) {
            var summaryLower = rule.summary.toLowerCase();
            for (var _a = 0, queryWords_2 = queryWords; _a < queryWords_2.length; _a++) {
                var word = queryWords_2[_a];
                if (summaryLower.includes(word)) {
                    score += 20;
                }
            }
        }
        // Content matches (if available)
        if (rule.content) {
            var contentLower = rule.content.toLowerCase();
            for (var _b = 0, queryWords_3 = queryWords; _b < queryWords_3.length; _b++) {
                var word = queryWords_3[_b];
                var matches = (contentLower.match(new RegExp(word, 'g')) || []).length;
                score += matches * 5;
            }
        }
        // Category bonus for specific categories
        if (queryWords.some(function (word) { return ['architecture', 'workflow', 'api', 'domain'].includes(word); })) {
            score += 10;
        }
        return score;
    };
    /**
     * Create a structured rule system error
     */
    ContextManager.prototype.createRuleSystemError = function (type, message, context, originalError) {
        var error = new Error(message);
        error.type = type;
        error.context = context;
        error.originalError = originalError;
        return error;
    };
    return ContextManager;
}());
exports.ContextManager = ContextManager;
