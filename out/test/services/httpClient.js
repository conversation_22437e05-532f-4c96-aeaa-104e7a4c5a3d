"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpClient = void 0;
var fs = require("node:fs");
var https = require("node:https");
var node_url_1 = require("node:url");
var HttpClient = /** @class */ (function () {
    function HttpClient(logger) {
        this.logger = logger;
    }
    HttpClient.prototype.get = function (url, headers) {
        var _this = this;
        if (headers === void 0) { headers = {}; }
        return new Promise(function (resolve, reject) {
            var parsedUrl = new node_url_1.URL(url);
            var options = {
                headers: __assign({}, headers),
                timeout: 30000,
            };
            _this.logger.debug("HttpClient GET: ".concat(url));
            var request = https.get(parsedUrl, options, function (res) {
                var data = "";
                res.setEncoding("utf8");
                res.on("data", function (chunk) {
                    data += chunk;
                });
                res.on("end", function () {
                    _this.logger.debug("HttpClient Response: ".concat(res.statusCode, " from ").concat(url));
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data,
                    });
                });
            });
            request.on("error", function (error) {
                _this.logger.error("HttpClient Error for ".concat(url, ": ").concat(error.message), error);
                reject(error);
            });
            request.on("timeout", function () {
                request.destroy();
                _this.logger.error("HttpClient Timeout for ".concat(url));
                reject(new Error("Request timed out after ".concat(options.timeout, "ms")));
            });
        });
    };
    HttpClient.prototype.downloadFile = function (url, targetPath, headers) {
        var _this = this;
        if (headers === void 0) { headers = {}; }
        return new Promise(function (resolve, reject) {
            var parsedUrl = new node_url_1.URL(url);
            var options = {
                headers: __assign(__assign({}, headers), { "User-Agent": "VS-Code-AIDD-Extension" }),
                timeout: 60000,
            };
            _this.logger.debug("HttpClient DownloadFile: ".concat(url, " to ").concat(targetPath));
            var request = https.get(parsedUrl, options, function (response) {
                if (response.statusCode === 200) {
                    var fileStream_1 = fs.createWriteStream(targetPath);
                    response.pipe(fileStream_1);
                    fileStream_1.on("finish", function () {
                        fileStream_1.close();
                        _this.logger.debug("Successfully downloaded file to ".concat(targetPath));
                        resolve();
                        /*
                         fileStream.close((err) => {
                           if (err) {
                             this.logger.error(
                               `Error closing file stream for ${targetPath}`,
                               err,
                             );
                             reject(err);
                           } else {
                             this.logger.debug(
                               `Successfully downloaded file to ${targetPath}`,
                             );
                             resolve();
                           }
                         });
                         */
                    });
                    fileStream_1.on("error", function (err) {
                        _this.logger.error("File stream error for ".concat(targetPath), err.message);
                        fs.unlink(targetPath, function (unlinkErr) {
                            if (unlinkErr) {
                                _this.logger.error("Failed to delete incomplete file ".concat(targetPath), unlinkErr);
                            }
                            reject(err);
                        });
                    });
                }
                else if (response.statusCode === 302 ||
                    response.statusCode === 301) {
                    var redirectUrl = response.headers.location;
                    if (redirectUrl) {
                        _this.logger.debug("Redirecting download from ".concat(url, " to ").concat(redirectUrl));
                        _this.downloadFile(redirectUrl, targetPath)
                            .then(resolve)
                            .catch(reject);
                    }
                    else {
                        var errorMsg = "Redirect (".concat(response.statusCode, ") without location header from ").concat(url);
                        _this.logger.error(errorMsg);
                        reject(new Error(errorMsg));
                    }
                }
                else {
                    var errorMsg = "Failed to download file from ".concat(url, ": Status Code ").concat(response.statusCode);
                    _this.logger.error(errorMsg);
                    response.resume();
                    reject(new Error(errorMsg));
                }
            });
            request.on("error", function (error) {
                _this.logger.error("HttpClient DownloadFile Error for ".concat(url, ": ").concat(error.message), error);
                reject(error);
            });
            request.on("timeout", function () {
                request.destroy();
                var errorMsg = "DownloadFile request timed out after ".concat(options.timeout, "ms for ").concat(url);
                _this.logger.error(errorMsg);
                reject(new Error(errorMsg));
            });
        });
    };
    return HttpClient;
}());
exports.HttpClient = HttpClient;
