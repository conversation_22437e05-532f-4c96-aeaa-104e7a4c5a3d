"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitManager = void 0;
var RateLimitManager = /** @class */ (function () {
    function RateLimitManager(logger) {
        this.logger = logger;
        this.currentRateLimit = null;
    }
    RateLimitManager.prototype.updateFromHeaders = function (headers) {
        var _a;
        var limit = headers["x-ratelimit-limit"];
        var remaining = headers["x-ratelimit-remaining"];
        var reset = headers["x-ratelimit-reset"];
        if (limit && remaining && reset) {
            var newLimit = {
                limit: Number.parseInt(Array.isArray(limit) ? limit[0] : limit, 10),
                remaining: Number.parseInt(Array.isArray(remaining) ? remaining[0] : remaining, 10),
                reset: Number.parseInt(Array.isArray(reset) ? reset[0] : reset, 10),
            };
            if (!this.currentRateLimit ||
                newLimit.remaining !== this.currentRateLimit.remaining ||
                newLimit.reset !== this.currentRateLimit.reset) {
                this.currentRateLimit = newLimit;
                this.logger.debug("Rate limit updated: ".concat(this.currentRateLimit.remaining, "/").concat(this.currentRateLimit.limit, ", reset at ").concat((_a = this.getResetTime()) === null || _a === void 0 ? void 0 : _a.toLocaleTimeString()));
            }
        }
        else if (this.currentRateLimit) {
            this.logger.warn("Rate limit headers missing in response.");
        }
    };
    RateLimitManager.prototype.isLimitExceeded = function () {
        if (this.currentRateLimit && this.currentRateLimit.remaining === 0) {
            var now = Math.floor(Date.now() / 1000);
            return now < this.currentRateLimit.reset;
        }
        return false;
    };
    RateLimitManager.prototype.getRateLimitInfo = function () {
        return this.currentRateLimit;
    };
    RateLimitManager.prototype.getResetTime = function () {
        if (this.currentRateLimit) {
            return new Date(this.currentRateLimit.reset * 1000);
        }
        return null;
    };
    return RateLimitManager;
}());
exports.RateLimitManager = RateLimitManager;
