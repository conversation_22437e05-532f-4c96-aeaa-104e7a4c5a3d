"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageService = exports.StorageKey = void 0;
var vscode = require("vscode");
var StorageKey;
(function (StorageKey) {
    StorageKey["RECENT_REPOSITORIES"] = "aidd.recentRepositories";
    StorageKey["LAST_REPOSITORY"] = "aidd.lastRepository";
})(StorageKey || (exports.StorageKey = StorageKey = {}));
var StorageService = /** @class */ (function () {
    function StorageService(context) {
        this.context = context;
    }
    StorageService.prototype.getRecentRepositories = function () {
        var repos = this.context.globalState.get(StorageKey.RECENT_REPOSITORIES, []);
        return repos;
    };
    StorageService.prototype.addRecentRepository = function (repository) {
        var _a;
        var repos = this.getRecentRepositories();
        var maxRecent = (_a = vscode.workspace
            .getConfiguration("aidd")
            .get("maxRecentRepositories")) !== null && _a !== void 0 ? _a : 5;
        var filteredRepos = repos.filter(function (repo) {
            return !(repo.owner === repository.owner && repo.name === repository.name);
        });
        filteredRepos.unshift(repository);
        var limitedRepos = filteredRepos.slice(0, maxRecent);
        this.context.globalState.update(StorageKey.RECENT_REPOSITORIES, limitedRepos);
        this.setLastRepository(repository);
    };
    StorageService.prototype.getLastRepository = function () {
        return this.context.globalState.get(StorageKey.LAST_REPOSITORY);
    };
    StorageService.prototype.setLastRepository = function (repository) {
        this.context.globalState.update(StorageKey.LAST_REPOSITORY, repository);
    };
    StorageService.prototype.clearStorage = function () {
        this.context.globalState.update(StorageKey.RECENT_REPOSITORIES, undefined);
        this.context.globalState.update(StorageKey.LAST_REPOSITORY, undefined);
    };
    return StorageService;
}());
exports.StorageService = StorageService;
