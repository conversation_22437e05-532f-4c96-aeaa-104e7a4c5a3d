"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextRetrievalTool = void 0;
var vscode = require("vscode");
var rules_1 = require("../types/rules");
/**
 * Chat tool for retrieving relevant rule context from GitHub repositories
 */
var ContextRetrievalTool = /** @class */ (function () {
    function ContextRetrievalTool(logger, contextManager) {
        this.logger = logger;
        this.contextManager = contextManager;
    }
    /**
     * Invoke the context retrieval tool
     */
    ContextRetrievalTool.prototype.invoke = function (options, token) {
        return __awaiter(this, void 0, void 0, function () {
            var validationError, params, result, formattedResult, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        this.logger.info("Context retrieval tool invoked with query: \"".concat(options.input.query, "\""));
                        validationError = this.validateInput(options.input);
                        if (validationError) {
                            return [2 /*return*/, new vscode.LanguageModelToolResult([
                                    new vscode.LanguageModelTextPart(validationError),
                                ])];
                        }
                        // Check for cancellation
                        if (token.isCancellationRequested) {
                            return [2 /*return*/, new vscode.LanguageModelToolResult([
                                    new vscode.LanguageModelTextPart("Context retrieval was cancelled."),
                                ])];
                        }
                        params = {
                            query: options.input.query.trim(),
                            maxRules: options.input.maxRules || 5,
                            service: options.input.service,
                            includeContent: options.input.includeContent !== false,
                        };
                        return [4 /*yield*/, this.contextManager.retrieveContext(params)];
                    case 1:
                        result = _a.sent();
                        // Check for cancellation after retrieval
                        if (token.isCancellationRequested) {
                            return [2 /*return*/, new vscode.LanguageModelToolResult([
                                    new vscode.LanguageModelTextPart("Context retrieval was cancelled."),
                                ])];
                        }
                        formattedResult = this.formatRetrievalResult(result);
                        this.logger.info("Context retrieval completed: ".concat(result.rules.length, " rules retrieved"));
                        return [2 /*return*/, new vscode.LanguageModelToolResult([
                                new vscode.LanguageModelTextPart(formattedResult),
                            ])];
                    case 2:
                        error_1 = _a.sent();
                        this.logger.error('Error in context retrieval tool:', error_1);
                        return [2 /*return*/, this.handleError(error_1)];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Prepare tool invocation with user confirmation
     */
    ContextRetrievalTool.prototype.prepareInvocation = function (options, _token) {
        return __awaiter(this, void 0, void 0, function () {
            var query, maxRules, service, confirmationMessage, confirmationMessages;
            var _a;
            return __generator(this, function (_b) {
                query = ((_a = options.input.query) === null || _a === void 0 ? void 0 : _a.trim()) || '';
                maxRules = options.input.maxRules || 5;
                service = options.input.service;
                confirmationMessage = "Retrieve development rules for: **\"".concat(query, "\"**");
                if (service) {
                    confirmationMessage += "\n- Service: ".concat(service);
                }
                confirmationMessage += "\n- Max rules: ".concat(maxRules);
                confirmationMessages = {
                    title: "Retrieve Development Rules",
                    message: new vscode.MarkdownString(confirmationMessage),
                };
                return [2 /*return*/, {
                        invocationMessage: "Searching for relevant development rules...",
                        confirmationMessages: confirmationMessages,
                    }];
            });
        });
    };
    /**
     * Validate input parameters
     */
    ContextRetrievalTool.prototype.validateInput = function (input) {
        if (!input.query || typeof input.query !== 'string') {
            return "❌ **Error**: Query parameter is required and must be a string.";
        }
        if (input.query.trim().length === 0) {
            return "❌ **Error**: Query cannot be empty.";
        }
        if (input.query.trim().length < 3) {
            return "❌ **Error**: Query must be at least 3 characters long.";
        }
        if (input.maxRules !== undefined) {
            if (typeof input.maxRules !== 'number' || input.maxRules < 1 || input.maxRules > 20) {
                return "❌ **Error**: maxRules must be a number between 1 and 20.";
            }
        }
        if (input.service !== undefined && typeof input.service !== 'string') {
            return "❌ **Error**: service parameter must be a string.";
        }
        return null;
    };
    /**
     * Format retrieval result for display
     */
    ContextRetrievalTool.prototype.formatRetrievalResult = function (result) {
        if (result.rules.length === 0) {
            return this.formatNoResultsMessage(result);
        }
        var parts = [];
        // Header with summary
        parts.push("# \uD83D\uDCCB Development Rules Context");
        parts.push("**Query**: \"".concat(result.query, "\""));
        parts.push("**Found**: ".concat(result.rules.length, " rules (").concat(result.totalFound, " total matches)"));
        if (result.repositoryStructure.repositoryUrl) {
            parts.push("**Repository**: [".concat(result.repositoryStructure.repositoryUrl.split('/').slice(-2).join('/'), "](").concat(result.repositoryStructure.repositoryUrl, ")"));
        }
        parts.push(''); // Empty line
        // Format each rule
        for (var i = 0; i < result.rules.length; i++) {
            var rule = result.rules[i];
            var score = result.scores[i];
            parts.push(this.formatRule(rule, score, i + 1));
            parts.push(''); // Empty line between rules
        }
        // Footer with repository info
        if (result.repositoryStructure.description) {
            parts.push('---');
            parts.push("**Repository Description**: ".concat(result.repositoryStructure.description));
        }
        return parts.join('\n');
    };
    /**
     * Format a single rule for display
     */
    ContextRetrievalTool.prototype.formatRule = function (rule, score, index) {
        var categoryInfo = rules_1.CATEGORY_INFO[rule.category];
        var parts = [];
        // Rule header
        parts.push("## ".concat(index, ". ").concat(categoryInfo.icon, " ").concat(rule.name));
        // Metadata
        var metadata = [];
        metadata.push("**Category**: ".concat(categoryInfo.name));
        metadata.push("**Service**: ".concat(rule.service));
        if (rule.version) {
            metadata.push("**Version**: ".concat(rule.version));
        }
        if (rule.specificity) {
            metadata.push("**Scope**: ".concat(rule.specificity));
        }
        metadata.push("**Relevance**: ".concat(Math.round(score), "%"));
        metadata.push("**File**: `".concat(rule.filename, "`"));
        parts.push(metadata.join(' • '));
        // Summary
        if (rule.summary) {
            parts.push('');
            parts.push("**Summary**: ".concat(rule.summary));
        }
        // Content (truncated if too long)
        if (rule.content) {
            parts.push('');
            parts.push('**Content**:');
            var maxContentLength = 1000;
            var content = rule.content;
            if (content.length > maxContentLength) {
                content = content.substring(0, maxContentLength) + '\n\n*[Content truncated...]*';
            }
            parts.push('```markdown');
            parts.push(content);
            parts.push('```');
        }
        return parts.join('\n');
    };
    /**
     * Format message when no results are found
     */
    ContextRetrievalTool.prototype.formatNoResultsMessage = function (result) {
        var parts = [];
        parts.push("# \uD83D\uDD0D No Development Rules Found");
        parts.push("**Query**: \"".concat(result.query, "\""));
        parts.push('');
        parts.push('No relevant development rules were found for your query.');
        parts.push('');
        parts.push('**Suggestions**:');
        parts.push('- Try using different keywords');
        parts.push('- Check if the repository contains rule files');
        parts.push('- Verify the repository structure includes a `main.mdc` file');
        parts.push('- Try broader search terms');
        if (result.repositoryStructure.totalRules > 0) {
            parts.push('');
            parts.push("**Available**: ".concat(result.repositoryStructure.totalRules, " total rules across ").concat(Object.keys(result.repositoryStructure.services).length, " services"));
            // List available services
            var services = Object.keys(result.repositoryStructure.services);
            if (services.length > 0) {
                parts.push('**Services**: ' + services.join(', '));
            }
        }
        return parts.join('\n');
    };
    /**
     * Handle errors and return appropriate error message
     */
    ContextRetrievalTool.prototype.handleError = function (error) {
        var errorMessage = "❌ **Error retrieving development rules**\n\n";
        if (this.isRuleSystemError(error)) {
            switch (error.type) {
                case rules_1.RuleSystemError.REPOSITORY_NOT_FOUND:
                    errorMessage += "**Repository not found or not accessible.**\n";
                    errorMessage += "Please ensure you have selected a valid repository.";
                    break;
                case rules_1.RuleSystemError.MAIN_MDC_NOT_FOUND:
                    errorMessage += "**Repository structure file (main.mdc) not found.**\n";
                    errorMessage += "The repository may not follow the expected structure.";
                    break;
                case rules_1.RuleSystemError.NETWORK_ERROR:
                    errorMessage += "**Network error occurred.**\n";
                    errorMessage += "Please check your internet connection and try again.";
                    break;
                case rules_1.RuleSystemError.PERMISSION_DENIED:
                    errorMessage += "**Permission denied.**\n";
                    errorMessage += "You may not have access to this repository.";
                    break;
                default:
                    errorMessage += "**".concat(error.type, "**: ").concat(error.message);
            }
        }
        else {
            errorMessage += "**Unexpected error**: ".concat(error.message);
        }
        errorMessage += "\n\nPlease try again or contact support if the issue persists.";
        return new vscode.LanguageModelToolResult([
            new vscode.LanguageModelTextPart(errorMessage),
        ]);
    };
    /**
     * Type guard to check if error is a rule system error
     */
    ContextRetrievalTool.prototype.isRuleSystemError = function (error) {
        return 'type' in error && Object.values(rules_1.RuleSystemError).includes(error.type);
    };
    return ContextRetrievalTool;
}());
exports.ContextRetrievalTool = ContextRetrievalTool;
