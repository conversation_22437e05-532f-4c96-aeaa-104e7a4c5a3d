"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitHubApiService = void 0;
var pathUtils = require("node:path");
var vscode = require("vscode");
var githubApiResponseHandler_1 = require("../utils/githubApiResponseHandler");
function mapGitTypeToContentType(gitType) {
    switch (gitType) {
        case "blob":
            return "file";
        case "tree":
            return "dir";
        case "commit":
            return "submodule";
        default:
            return "file";
    }
}
var GitHubApiService = /** @class */ (function () {
    function GitHubApiService(httpClient, rateLimitManager, logger) {
        this.httpClient = httpClient;
        this.rateLimitManager = rateLimitManager;
        this.logger = logger;
        this.baseApiUrl = "https://api.github.com";
        this.responseHandler = new githubApiResponseHandler_1.GitHubApiResponseHandler(this.rateLimitManager, this.logger);
    }
    GitHubApiService.prototype.makeApiRequest = function (apiUrl_1) {
        return __awaiter(this, arguments, void 0, function (apiUrl, isRawContent) {
            var headers, configuration, token, httpResponse, error_1;
            if (isRawContent === void 0) { isRawContent = false; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        headers = {
                            "User-Agent": "VS-Code-AIDD-Extension",
                            Accept: isRawContent
                                ? "application/vnd.github.raw"
                                : "application/vnd.github.v3+json",
                        };
                        configuration = vscode.workspace.getConfiguration("aidd");
                        token = configuration.get("githubToken");
                        if (token) {
                            headers.Authorization = "token ".concat(token);
                            this.logger.debug("GitHub API Request (Auth): ".concat(apiUrl));
                        }
                        else {
                            this.logger.debug("GitHub API Request (No Auth): ".concat(apiUrl));
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.httpClient.get(apiUrl, headers)];
                    case 2:
                        httpResponse = _a.sent();
                        return [2 /*return*/, this.responseHandler.handleResponse(httpResponse, isRawContent)];
                    case 3:
                        error_1 = _a.sent();
                        this.logger.error("HTTP Client error for ".concat(apiUrl), error_1);
                        return [2 /*return*/, {
                                success: false,
                                error: error_1 instanceof Error
                                    ? error_1
                                    : new Error("HTTP request failed: ".concat(String(error_1))),
                            }];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    GitHubApiService.prototype.fetchRepositoryContent = function (repository_1) {
        return __awaiter(this, arguments, void 0, function (repository, path) {
            var owner, name, branch, apiUrl, result, data;
            if (path === void 0) { path = ""; }
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        owner = repository.owner, name = repository.name, branch = repository.branch;
                        apiUrl = "".concat(this.baseApiUrl, "/repos/").concat(owner, "/").concat(name, "/contents/").concat(path);
                        if (branch) {
                            apiUrl += "?ref=".concat(branch);
                        }
                        return [4 /*yield*/, this.makeApiRequest(apiUrl)];
                    case 1:
                        result = _a.sent();
                        if (!result.success) {
                            return [2 /*return*/, result];
                        }
                        data = Array.isArray(result.data) ? result.data : [result.data];
                        return [2 /*return*/, { success: true, data: data }];
                }
            });
        });
    };
    GitHubApiService.prototype.fetchRepositoryContentRecursive = function (repository_1) {
        return __awaiter(this, arguments, void 0, function (repository, _path, _maxDepth) {
            var owner, name, _a, branch, branchApiUrl, branchResult, treeSha, treeApiUrl, treeResult, _b, tree, truncated, allContents;
            var _this = this;
            var _c, _d, _e;
            if (_path === void 0) { _path = ""; }
            if (_maxDepth === void 0) { _maxDepth = Number.POSITIVE_INFINITY; }
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        owner = repository.owner, name = repository.name, _a = repository.branch, branch = _a === void 0 ? "main" : _a;
                        this.logger.info("Fetching recursive tree for ".concat(owner, "/").concat(name, ", branch: ").concat(branch));
                        branchApiUrl = "".concat(this.baseApiUrl, "/repos/").concat(owner, "/").concat(name, "/branches/").concat(branch || "main");
                        return [4 /*yield*/, this.makeApiRequest(branchApiUrl)];
                    case 1:
                        branchResult = _f.sent();
                        if (!branchResult.success) {
                            this.logger.error("Failed to fetch branch details for ".concat(branch), branchResult.error);
                            return [2 /*return*/, {
                                    success: false,
                                    error: new Error("Failed to get branch details: ".concat(branchResult.error.message)),
                                }];
                        }
                        treeSha = (_e = (_d = (_c = branchResult.data.commit) === null || _c === void 0 ? void 0 : _c.commit) === null || _d === void 0 ? void 0 : _d.tree) === null || _e === void 0 ? void 0 : _e.sha;
                        if (!treeSha) {
                            this.logger.error("Could not find tree SHA for branch ".concat(branch), branchResult.data);
                            return [2 /*return*/, {
                                    success: false,
                                    error: new Error("Could not find root tree SHA for the branch."),
                                }];
                        }
                        this.logger.debug("Found root tree SHA: ".concat(treeSha, " for branch ").concat(branch));
                        treeApiUrl = "".concat(this.baseApiUrl, "/repos/").concat(owner, "/").concat(name, "/git/trees/").concat(treeSha, "?recursive=1");
                        return [4 /*yield*/, this.makeApiRequest(treeApiUrl)];
                    case 2:
                        treeResult = _f.sent();
                        if (!treeResult.success) {
                            this.logger.error("Failed to fetch recursive tree ".concat(treeSha), treeResult.error);
                            return [2 /*return*/, {
                                    success: false,
                                    error: new Error("Failed to get recursive tree: ".concat(treeResult.error.message)),
                                }];
                        }
                        _b = treeResult.data, tree = _b.tree, truncated = _b.truncated;
                        if (truncated) {
                            this.logger.warn("GitHub API response for recursive tree was truncated for ".concat(owner, "/").concat(name, ". Results may be incomplete."));
                        }
                        allContents = tree.map(function (item) {
                            var _a, _b;
                            var contentType = mapGitTypeToContentType(item.type);
                            var itemName = pathUtils.basename(item.path);
                            var itemHtmlUrl = "https://github.com/".concat(owner, "/").concat(name, "/").concat(contentType === "file" ? "blob" : "tree", "/").concat(branch, "/").concat(item.path);
                            return {
                                name: itemName,
                                path: item.path,
                                sha: item.sha,
                                size: (_a = item.size) !== null && _a !== void 0 ? _a : 0,
                                url: "".concat(_this.baseApiUrl, "/repos/").concat(owner, "/").concat(name, "/contents/").concat(item.path, "?ref=").concat(branch),
                                html_url: itemHtmlUrl,
                                git_url: (_b = item.url) !== null && _b !== void 0 ? _b : "",
                                download_url: contentType === "file"
                                    ? "".concat(_this.baseApiUrl, "/repos/").concat(owner, "/").concat(name, "/contents/").concat(item.path, "?ref=").concat(branch)
                                    : null,
                                type: contentType,
                            };
                        });
                        this.logger.info("Recursive tree fetch complete for ".concat(owner, "/").concat(name, ". Found ").concat(allContents.length, " items. Truncated: ").concat(truncated));
                        return [2 /*return*/, { success: true, data: allContents }];
                }
            });
        });
    };
    GitHubApiService.prototype.fetchFileContent = function (downloadUrl) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.makeApiRequest(downloadUrl, true)];
            });
        });
    };
    return GitHubApiService;
}());
exports.GitHubApiService = GitHubApiService;
