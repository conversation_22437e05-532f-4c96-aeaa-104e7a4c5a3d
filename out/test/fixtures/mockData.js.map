{"version": 3, "file": "mockData.js", "sourceRoot": "", "sources": ["../../../src/test/fixtures/mockData.ts"], "names": [], "mappings": ";;;AAAA,6CAAiD;AAUjD,yBAAyB;AACZ,QAAA,cAAc,GAAqB;IAC9C,KAAK,EAAE,UAAU;IACjB,IAAI,EAAE,WAAW;IACjB,MAAM,EAAE,MAAM;CACf,CAAC;AAEF,sBAAsB;AACT,QAAA,iBAAiB,GAAoB;IAChD;QACE,IAAI,EAAE,2BAA2B;QACjC,IAAI,EAAE,mCAAmC;QACzC,GAAG,EAAE,QAAQ;QACb,IAAI,EAAE,IAAI;QACV,GAAG,EAAE,4FAA4F;QACjG,QAAQ,EAAE,mFAAmF;QAC7F,OAAO,EAAE,kEAAkE;QAC3E,YAAY,EAAE,qGAAqG;QACnH,IAAI,EAAE,MAAM;KACb;IACD;QACE,IAAI,EAAE,0BAA0B;QAChC,IAAI,EAAE,kCAAkC;QACxC,GAAG,EAAE,QAAQ;QACb,IAAI,EAAE,IAAI;QACV,GAAG,EAAE,2FAA2F;QAChG,QAAQ,EAAE,kFAAkF;QAC5F,OAAO,EAAE,kEAAkE;QAC3E,YAAY,EAAE,oGAAoG;QAClH,IAAI,EAAE,MAAM;KACb;IACD;QACE,IAAI,EAAE,qCAAqC;QAC3C,IAAI,EAAE,8CAA8C;QACpD,GAAG,EAAE,QAAQ;QACb,IAAI,EAAE,IAAI;QACV,GAAG,EAAE,uGAAuG;QAC5G,QAAQ,EAAE,8FAA8F;QACxG,OAAO,EAAE,kEAAkE;QAC3E,YAAY,EAAE,gHAAgH;QAC9H,IAAI,EAAE,MAAM;KACb;CACF,CAAC;AAEF,qBAAqB;AACR,QAAA,gBAAgB,GAAoB;IAC/C;QACE,QAAQ,EAAE,2BAA2B;QACrC,IAAI,EAAE,oBAAoB;QAC1B,QAAQ,EAAE,oBAAY,CAAC,YAAY;QACnC,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE,mCAAmC;QACzC,OAAO,EAAE,SAAS;QAClB,WAAW,EAAE,SAAS;KACvB;IACD;QACE,QAAQ,EAAE,0BAA0B;QACpC,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,oBAAY,CAAC,SAAS;QAChC,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE,kCAAkC;QACxC,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,SAAS;KACvB;IACD;QACE,QAAQ,EAAE,qCAAqC;QAC/C,IAAI,EAAE,qBAAqB;QAC3B,QAAQ,EAAE,oBAAY,CAAC,OAAO;QAC9B,OAAO,EAAE,UAAU;QACnB,IAAI,EAAE,8CAA8C;QACpD,OAAO,EAAE,SAAS;QAClB,WAAW,EAAE,UAAU;KACxB;CACF,CAAC;AAEF,oBAAoB;AACP,QAAA,eAAe,GAAmB;IAC7C;QACE,GAAG,wBAAgB,CAAC,CAAC,CAAC;QACtB,OAAO,EAAE,4EAA4E;QACrF,OAAO,EAAE,oDAAoD;KAC9D;IACD;QACE,GAAG,wBAAgB,CAAC,CAAC,CAAC;QACtB,OAAO,EAAE,wEAAwE;QACjF,OAAO,EAAE,gDAAgD;KAC1D;IACD;QACE,GAAG,wBAAgB,CAAC,CAAC,CAAC;QACtB,OAAO,EAAE,2FAA2F;QACpG,OAAO,EAAE,uDAAuD;KACjE;CACF,CAAC;AAEF,4BAA4B;AACf,QAAA,uBAAuB,GAAyB;IAC3D,WAAW,EAAE,oCAAoC;IACjD,QAAQ,EAAE;QACR,OAAO,EAAE;YACP,IAAI,EAAE,iBAAiB;YACvB,UAAU,EAAE,CAAC,oBAAY,CAAC,YAAY,EAAE,oBAAY,CAAC,SAAS,CAAC;YAC/D,UAAU,EAAE;gBACV,CAAC,oBAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9B,CAAC,oBAAY,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3B,CAAC,oBAAY,CAAC,OAAO,CAAC,EAAE,CAAC;gBACzB,CAAC,oBAAY,CAAC,YAAY,CAAC,EAAE,CAAC;aAC/B;SACF;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,kBAAkB;YACxB,UAAU,EAAE,CAAC,oBAAY,CAAC,OAAO,CAAC;YAClC,UAAU,EAAE;gBACV,CAAC,oBAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9B,CAAC,oBAAY,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3B,CAAC,oBAAY,CAAC,OAAO,CAAC,EAAE,CAAC;gBACzB,CAAC,oBAAY,CAAC,YAAY,CAAC,EAAE,CAAC;aAC/B;SACF;KACF;IACD,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;IAC5C,aAAa,EAAE,uCAAuC;CACvD,CAAC;AAEF,oCAAoC;AACvB,QAAA,0BAA0B,GAA4B;IACjE,KAAK,EAAE,uBAAuB;IAC9B,QAAQ,EAAE,CAAC;IACX,cAAc,EAAE,IAAI;CACrB,CAAC;AAEF,gCAAgC;AACnB,QAAA,0BAA0B,GAA4B;IACjE,KAAK,EAAE,CAAC,uBAAe,CAAC,CAAC,CAAC,CAAC;IAC3B,UAAU,EAAE,CAAC;IACb,KAAK,EAAE,uBAAuB;IAC9B,MAAM,EAAE,CAAC,GAAG,CAAC;IACb,mBAAmB,EAAE,+BAAuB;CAC7C,CAAC;AAEF,wBAAwB;AACX,QAAA,kBAAkB,GAAG;;;;;;;;;;;CAWjC,CAAC;AAEF,8BAA8B;AACjB,QAAA,wBAAwB,GAA2B;IAC9D,mCAAmC,EAAE,uBAAe,CAAC,CAAC,CAAC,CAAC,OAAQ;IAChE,kCAAkC,EAAE,uBAAe,CAAC,CAAC,CAAC,CAAC,OAAQ;IAC/D,8CAA8C,EAAE,uBAAe,CAAC,CAAC,CAAC,CAAC,OAAQ;IAC3E,UAAU,EAAE,0BAAkB;CAC/B,CAAC"}