"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockFileContentResponses = exports.mockMainMdcContent = exports.mockContextRetrievalResult = exports.mockContextRetrievalParams = exports.mockRepositoryStructure = exports.mockRuleContent = exports.mockRuleMetadata = exports.mockGithubContent = exports.mockRepository = void 0;
const rules_1 = require("../../types/rules");
// Mock GitHub Repository
exports.mockRepository = {
    owner: 'test-org',
    name: 'test-repo',
    branch: 'main'
};
// Mock GitHub Content
exports.mockGithubContent = [
    {
        name: '00-clean-architecture.mdc',
        path: 'backend/00-clean-architecture.mdc',
        sha: 'abc123',
        size: 1024,
        url: 'https://api.github.com/repos/test-org/test-repo/contents/backend/00-clean-architecture.mdc',
        html_url: 'https://github.com/test-org/test-repo/blob/main/backend/00-clean-architecture.mdc',
        git_url: 'https://api.github.com/repos/test-org/test-repo/git/blobs/abc123',
        download_url: 'https://api.github.com/repos/test-org/test-repo/contents/backend/00-clean-architecture.mdc?ref=main',
        type: 'file'
    },
    {
        name: '<EMAIL>',
        path: 'backend/<EMAIL>',
        sha: 'def456',
        size: 2048,
        url: 'https://api.github.com/repos/test-org/test-repo/contents/backend/<EMAIL>',
        html_url: 'https://github.com/test-org/test-repo/blob/main/backend/<EMAIL>',
        git_url: 'https://api.github.com/repos/test-org/test-repo/git/blobs/def456',
        download_url: 'https://api.github.com/repos/test-org/test-repo/contents/backend/<EMAIL>?ref=main',
        type: 'file'
    },
    {
        name: '02-rest-api-guidelines-frontend.mdc',
        path: 'frontend/02-rest-api-guidelines-frontend.mdc',
        sha: 'ghi789',
        size: 1536,
        url: 'https://api.github.com/repos/test-org/test-repo/contents/frontend/02-rest-api-guidelines-frontend.mdc',
        html_url: 'https://github.com/test-org/test-repo/blob/main/frontend/02-rest-api-guidelines-frontend.mdc',
        git_url: 'https://api.github.com/repos/test-org/test-repo/git/blobs/ghi789',
        download_url: 'https://api.github.com/repos/test-org/test-repo/contents/frontend/02-rest-api-guidelines-frontend.mdc?ref=main',
        type: 'file'
    }
];
// Mock Rule Metadata
exports.mockRuleMetadata = [
    {
        filename: '00-clean-architecture.mdc',
        name: 'clean-architecture',
        category: rules_1.RuleCategory.ARCHITECTURE,
        service: 'backend',
        path: 'backend/00-clean-architecture.mdc',
        version: undefined,
        specificity: undefined
    },
    {
        filename: '<EMAIL>',
        name: 'pr-workflow',
        category: rules_1.RuleCategory.WORKFLOWS,
        service: 'backend',
        path: 'backend/<EMAIL>',
        version: '1.2.0',
        specificity: undefined
    },
    {
        filename: '02-rest-api-guidelines-frontend.mdc',
        name: 'rest-api-guidelines',
        category: rules_1.RuleCategory.OPENAPI,
        service: 'frontend',
        path: 'frontend/02-rest-api-guidelines-frontend.mdc',
        version: undefined,
        specificity: 'frontend'
    }
];
// Mock Rule Content
exports.mockRuleContent = [
    {
        ...exports.mockRuleMetadata[0],
        content: '# Clean Architecture\n\nThis rule defines clean architecture principles...',
        summary: 'Clean architecture principles for backend services'
    },
    {
        ...exports.mockRuleMetadata[1],
        content: '# PR Workflow v1.2.0\n\nThis rule defines the pull request workflow...',
        summary: 'Pull request workflow guidelines version 1.2.0'
    },
    {
        ...exports.mockRuleMetadata[2],
        content: '# REST API Guidelines (Frontend)\n\nThis rule defines REST API guidelines for frontend...',
        summary: 'REST API guidelines specific to frontend applications'
    }
];
// Mock Repository Structure
exports.mockRepositoryStructure = {
    description: 'Test repository for xendit-copilot',
    services: {
        backend: {
            name: 'Backend Service',
            categories: [rules_1.RuleCategory.ARCHITECTURE, rules_1.RuleCategory.WORKFLOWS],
            ruleCounts: {
                [rules_1.RuleCategory.ARCHITECTURE]: 1,
                [rules_1.RuleCategory.WORKFLOWS]: 1,
                [rules_1.RuleCategory.OPENAPI]: 0,
                [rules_1.RuleCategory.DOMAIN_RULES]: 0
            }
        },
        frontend: {
            name: 'Frontend Service',
            categories: [rules_1.RuleCategory.OPENAPI],
            ruleCounts: {
                [rules_1.RuleCategory.ARCHITECTURE]: 0,
                [rules_1.RuleCategory.WORKFLOWS]: 0,
                [rules_1.RuleCategory.OPENAPI]: 1,
                [rules_1.RuleCategory.DOMAIN_RULES]: 0
            }
        }
    },
    totalRules: 3,
    lastParsed: new Date('2024-01-01T00:00:00Z'),
    repositoryUrl: 'https://github.com/test-org/test-repo'
};
// Mock Context Retrieval Parameters
exports.mockContextRetrievalParams = {
    query: 'architecture patterns',
    maxRules: 5,
    includeContent: true
};
// Mock Context Retrieval Result
exports.mockContextRetrievalResult = {
    rules: [exports.mockRuleContent[0]],
    totalFound: 1,
    query: 'architecture patterns',
    scores: [100],
    repositoryStructure: exports.mockRepositoryStructure
};
// Mock main.mdc content
exports.mockMainMdcContent = `# Test Repository

This is a test repository for xendit-copilot testing.

## Backend Service

Backend service rules and guidelines.

## Frontend Service

Frontend service rules and guidelines.
`;
// Mock file content responses
exports.mockFileContentResponses = {
    'backend/00-clean-architecture.mdc': exports.mockRuleContent[0].content,
    'backend/<EMAIL>': exports.mockRuleContent[1].content,
    'frontend/02-rest-api-guidelines-frontend.mdc': exports.mockRuleContent[2].content,
    'main.mdc': exports.mockMainMdcContent
};
//# sourceMappingURL=mockData.js.map