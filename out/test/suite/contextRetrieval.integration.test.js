"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const nock = __importStar(require("nock"));
const contextManager_1 = require("../../services/contextManager");
const contextRetrievalTool_1 = require("../../tools/contextRetrievalTool");
const logger_1 = require("../../services/logger");
const storage_1 = require("../../services/storage");
const github_1 = require("../../api/github");
const httpClient_1 = require("../../services/httpClient");
const rateLimitManager_1 = require("../../services/rateLimitManager");
const mockData_1 = require("../fixtures/mockData");
suite('Context Retrieval Integration Tests', () => {
    let contextManager;
    let contextRetrievalTool;
    let logger;
    let storageService;
    let githubService;
    setup(async () => {
        // Create real services for integration testing
        logger = new logger_1.Logger('Test', false);
        // Mock VS Code context for storage service
        const mockContext = {
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => []
            },
            workspaceState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => []
            }
        };
        storageService = new storage_1.StorageService(mockContext);
        const httpClient = new httpClient_1.HttpClient(logger);
        const rateLimitManager = new rateLimitManager_1.RateLimitManager(logger);
        githubService = new github_1.GitHubApiService(httpClient, rateLimitManager, logger);
        contextManager = new contextManager_1.ContextManager(logger, githubService, storageService);
        contextRetrievalTool = new contextRetrievalTool_1.ContextRetrievalTool(logger, contextManager);
        // Set up the test repository
        storageService.setLastRepository(mockData_1.mockRepository);
    });
    teardown(() => {
        nock.cleanAll();
    });
    test('End-to-end context retrieval with mocked GitHub API', async () => {
        // Mock GitHub API responses
        const baseUrl = 'https://api.github.com';
        // Mock main.mdc fetch
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/main.mdc')
            .reply(200, [{
                name: 'main.mdc',
                path: 'main.mdc',
                download_url: `${baseUrl}/repos/test-org/test-repo/contents/main.mdc?ref=main`,
                type: 'file',
                sha: 'main123',
                size: 1024,
                url: `${baseUrl}/repos/test-org/test-repo/contents/main.mdc`,
                html_url: 'https://github.com/test-org/test-repo/blob/main/main.mdc',
                git_url: `${baseUrl}/repos/test-org/test-repo/git/blobs/main123`
            }]);
        // Mock main.mdc content
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/main.mdc?ref=main')
            .reply(200, mockData_1.mockMainMdcContent);
        // Mock root directory listing for auto-discovery
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/')
            .reply(200, [
            {
                name: 'backend',
                path: 'backend',
                type: 'dir',
                sha: 'backend123',
                size: 0,
                url: `${baseUrl}/repos/test-org/test-repo/contents/backend`,
                html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
                git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/backend123`
            },
            {
                name: 'frontend',
                path: 'frontend',
                type: 'dir',
                sha: 'frontend123',
                size: 0,
                url: `${baseUrl}/repos/test-org/test-repo/contents/frontend`,
                html_url: 'https://github.com/test-org/test-repo/tree/main/frontend',
                git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/frontend123`
            }
        ]);
        // Mock service directory listings
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/backend')
            .reply(200, mockData_1.mockGithubContent.filter(item => item.path.startsWith('backend/')));
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/frontend')
            .reply(200, mockData_1.mockGithubContent.filter(item => item.path.startsWith('frontend/')));
        // Mock file content downloads
        for (const [path, content] of Object.entries(mockData_1.mockFileContentResponses)) {
            if (path !== 'main.mdc') {
                nock(baseUrl)
                    .get(`/repos/test-org/test-repo/contents/${path}`)
                    .reply(200, [{
                        name: path.split('/').pop(),
                        path: path,
                        download_url: `${baseUrl}/repos/test-org/test-repo/contents/${path}?ref=main`,
                        type: 'file',
                        sha: 'file123',
                        size: content.length,
                        url: `${baseUrl}/repos/test-org/test-repo/contents/${path}`,
                        html_url: `https://github.com/test-org/test-repo/blob/main/${path}`,
                        git_url: `${baseUrl}/repos/test-org/test-repo/git/blobs/file123`
                    }]);
                nock(baseUrl)
                    .get(`/repos/test-org/test-repo/contents/${path}?ref=main`)
                    .reply(200, content);
            }
        }
        // Test the context retrieval tool
        const mockOptions = {
            input: {
                query: 'architecture patterns',
                maxRules: 5,
                includeContent: true
            }
        };
        const mockToken = {};
        const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
        // Verify the result
        assert.ok(result instanceof vscode.LanguageModelToolResult);
        const parts = result.content;
        assert.ok(parts.length > 0);
        const firstPart = parts[0];
        if (firstPart instanceof vscode.LanguageModelTextPart) {
            // Should find at least one relevant rule
            assert.ok(firstPart.value.includes('relevant rules') || firstPart.value.includes('No relevant rules'));
            // If rules were found, verify the format
            if (firstPart.value.includes('relevant rules')) {
                assert.ok(firstPart.value.includes('**Rule'));
                assert.ok(firstPart.value.includes('Category:'));
                assert.ok(firstPart.value.includes('Service:'));
            }
        }
    });
    test('Context retrieval with no repository selected', async () => {
        // Clear the repository
        storageService.setLastRepository(null);
        const mockOptions = {
            input: {
                query: 'architecture patterns',
                maxRules: 5
            }
        };
        const mockToken = {};
        const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
        const parts = result.content;
        const firstPart = parts[0];
        if (firstPart instanceof vscode.LanguageModelTextPart) {
            assert.ok(firstPart.value.includes('Error') || firstPart.value.includes('No repository'));
        }
    });
    test('Context retrieval with GitHub API errors', async () => {
        // Set up repository
        storageService.setLastRepository(mockData_1.mockRepository);
        // Mock GitHub API to return errors
        nock('https://api.github.com')
            .get('/repos/test-org/test-repo/contents/main.mdc')
            .reply(404, { message: 'Not Found' });
        nock('https://api.github.com')
            .get('/repos/test-org/test-repo/contents/')
            .reply(403, { message: 'API rate limit exceeded' });
        const mockOptions = {
            input: {
                query: 'architecture patterns',
                maxRules: 5
            }
        };
        const mockToken = {};
        const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
        const parts = result.content;
        const firstPart = parts[0];
        if (firstPart instanceof vscode.LanguageModelTextPart) {
            // Should handle the error gracefully
            assert.ok(firstPart.value.includes('Error') || firstPart.value.includes('No relevant rules'));
        }
    });
    test('Context manager caching behavior', async () => {
        // Set up repository
        storageService.setLastRepository(mockData_1.mockRepository);
        // Mock successful GitHub API responses
        const baseUrl = 'https://api.github.com';
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/main.mdc')
            .reply(404); // main.mdc not found, should trigger auto-discovery
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/')
            .reply(200, [
            {
                name: 'backend',
                path: 'backend',
                type: 'dir',
                sha: 'backend123',
                size: 0,
                url: `${baseUrl}/repos/test-org/test-repo/contents/backend`,
                html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
                git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/backend123`
            }
        ]);
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/backend')
            .reply(200, []);
        // First call should fetch from API
        const structure1 = await contextManager.getRepositoryStructure();
        assert.ok(structure1);
        // Second call should use cache (no additional API calls expected)
        const structure2 = await contextManager.getRepositoryStructure();
        assert.strictEqual(structure1, structure2);
        // Clear cache and verify it refetches
        contextManager.clearCache();
        // Set up the same mock again for the refetch
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/')
            .reply(200, [
            {
                name: 'backend',
                path: 'backend',
                type: 'dir',
                sha: 'backend123',
                size: 0,
                url: `${baseUrl}/repos/test-org/test-repo/contents/backend`,
                html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
                git_url: `${baseUrl}/repos/test-org/test-repo/git/trees/backend123`
            }
        ]);
        nock(baseUrl)
            .get('/repos/test-org/test-repo/contents/backend')
            .reply(200, []);
        const structure3 = await contextManager.getRepositoryStructure();
        assert.ok(structure3);
        assert.notStrictEqual(structure1, structure3); // Should be a new instance
    });
});
//# sourceMappingURL=contextRetrieval.integration.test.js.map