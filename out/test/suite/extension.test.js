"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const sinon = __importStar(require("sinon"));
suite('Extension Integration Tests', () => {
    let sandbox;
    setup(() => {
        sandbox = sinon.createSandbox();
    });
    teardown(() => {
        sandbox.restore();
    });
    test('Extension should be present and activate', async () => {
        const extension = vscode.extensions.getExtension('undefined_publisher.xendit-copilot');
        assert.ok(extension, 'Extension should be present');
        if (!extension.isActive) {
            await extension.activate();
        }
        assert.ok(extension.isActive, 'Extension should be active');
    });
    test('Chat tools should be registered', async () => {
        // Wait for extension to fully activate
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Check if our xendit-copilot tool is registered
        const tools = vscode.lm.tools;
        const xenditTool = tools.find(tool => tool.name === 'xendit-copilot_contextRetrieval');
        assert.ok(xenditTool, 'xendit-copilot_contextRetrieval tool should be registered');
    });
    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands();
        const expectedCommands = [
            'aidd.setRepository',
            'aidd.refresh',
            'aidd.toggleSelection',
            'aidd.downloadSelected',
            'aidd.welcome',
            'aidd.openSettings',
            'aidd.clearStorage',
            'aidd.refreshRuleStatus'
        ];
        for (const command of expectedCommands) {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        }
    });
    test('Views should be registered', async () => {
        // Check if the welcome view is available
        const welcomeView = vscode.window.createWebviewPanel('test', 'Test', vscode.ViewColumn.One, {});
        assert.ok(welcomeView, 'Should be able to create webview panels');
        welcomeView.dispose();
    });
    test('Configuration should be available', () => {
        const config = vscode.workspace.getConfiguration('aidd');
        // Test that configuration properties exist
        const githubToken = config.get('githubToken');
        const maxRecentRepositories = config.get('maxRecentRepositories');
        const showWelcomeOnStartup = config.get('showWelcomeOnStartup');
        assert.strictEqual(typeof githubToken, 'string');
        assert.strictEqual(typeof maxRecentRepositories, 'number');
        assert.strictEqual(typeof showWelcomeOnStartup, 'boolean');
    });
    test('Extension should handle activation events', async () => {
        // Test that the extension activates properly
        const extension = vscode.extensions.getExtension('undefined_publisher.xendit-copilot');
        assert.ok(extension);
        if (!extension.isActive) {
            await extension.activate();
        }
        // Extension should be active without errors
        assert.ok(extension.isActive);
    });
});
//# sourceMappingURL=extension.test.js.map