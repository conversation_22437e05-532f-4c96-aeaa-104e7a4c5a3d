{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../../src/test/suite/extension.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AACjC,6CAA+B;AAE/B,KAAK,CAAC,6BAA6B,EAAE,GAAG,EAAE;IACxC,IAAI,OAA2B,CAAC;IAEhC,KAAK,CAAC,GAAG,EAAE;QACT,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,GAAG,EAAE;QACZ,OAAO,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QAC1D,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,oCAAoC,CAAC,CAAC;QACvF,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,6BAA6B,CAAC,CAAC;QAEpD,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxB,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC;QAED,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,4BAA4B,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QACjD,uCAAuC;QACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAExD,iDAAiD;QACjD,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;QAC9B,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,iCAAiC,CAAC,CAAC;QAEvF,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,2DAA2D,CAAC,CAAC;IACrF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAErD,MAAM,gBAAgB,GAAG;YACvB,oBAAoB;YACpB,cAAc;YACd,sBAAsB;YACtB,uBAAuB;YACvB,cAAc;YACd,mBAAmB;YACnB,mBAAmB;YACnB,wBAAwB;SACzB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;YACvC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,OAAO,uBAAuB,CAAC,CAAC;QACnF,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QAC5C,yCAAyC;QACzC,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAClD,MAAM,EACN,MAAM,EACN,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB,EAAE,CACH,CAAC;QAEF,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,yCAAyC,CAAC,CAAC;QAClE,WAAW,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEzD,2CAA2C;QAC3C,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC9C,MAAM,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAClE,MAAM,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEhE,MAAM,CAAC,WAAW,CAAC,OAAO,WAAW,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,CAAC,WAAW,CAAC,OAAO,qBAAqB,EAAE,QAAQ,CAAC,CAAC;QAC3D,MAAM,CAAC,WAAW,CAAC,OAAO,oBAAoB,EAAE,SAAS,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;QAC3D,6CAA6C;QAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,oCAAoC,CAAC,CAAC;QACvF,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;QAErB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxB,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC;QAED,4CAA4C;QAC5C,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}