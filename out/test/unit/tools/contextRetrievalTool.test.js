"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const sinon = __importStar(require("sinon"));
const vscode = __importStar(require("vscode"));
const contextRetrievalTool_1 = require("../../../tools/contextRetrievalTool");
const mockData_1 = require("../../fixtures/mockData");
suite('ContextRetrievalTool Unit Tests', () => {
    let contextRetrievalTool;
    let mockLogger;
    let mockContextManager;
    setup(() => {
        mockLogger = {
            debug: sinon.stub(),
            info: sinon.stub(),
            warn: sinon.stub(),
            error: sinon.stub()
        };
        mockContextManager = {
            retrieveContext: sinon.stub(),
            getRepositoryStructure: sinon.stub(),
            searchRules: sinon.stub(),
            getRuleContent: sinon.stub(),
            clearCache: sinon.stub(),
            initializeSession: sinon.stub(),
            getSession: sinon.stub()
        };
        contextRetrievalTool = new contextRetrievalTool_1.ContextRetrievalTool(mockLogger, mockContextManager);
    });
    teardown(() => {
        sinon.restore();
    });
    suite('invoke', () => {
        test('should retrieve context successfully with valid parameters', async () => {
            const mockOptions = {
                input: {
                    query: 'architecture patterns',
                    maxRules: 5,
                    includeContent: true
                }
            };
            const mockToken = {};
            mockContextManager.retrieveContext.resolves(mockData_1.mockContextRetrievalResult);
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            assert.ok(result instanceof vscode.LanguageModelToolResult);
            // Verify the result contains expected content
            const parts = result.content;
            assert.ok(parts.length > 0);
            // Check if the first part is text and contains expected information
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('Found 1 relevant rules'));
                assert.ok(firstPart.value.includes('clean-architecture'));
                assert.ok(firstPart.value.includes('Architecture'));
            }
        });
        test('should handle missing query parameter', async () => {
            const mockOptions = {
                input: {
                    maxRules: 5
                    // Missing query
                }
            };
            const mockToken = {};
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            const parts = result.content;
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('Error: Query parameter is required'));
            }
        });
        test('should handle empty query parameter', async () => {
            const mockOptions = {
                input: {
                    query: '',
                    maxRules: 5
                }
            };
            const mockToken = {};
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            const parts = result.content;
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('Error: Query cannot be empty'));
            }
        });
        test('should handle query that is too long', async () => {
            const longQuery = 'a'.repeat(501); // Exceeds 500 character limit
            const mockOptions = {
                input: {
                    query: longQuery,
                    maxRules: 5
                }
            };
            const mockToken = {};
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            const parts = result.content;
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('Error: Query is too long'));
            }
        });
        test('should handle invalid maxRules parameter', async () => {
            const mockOptions = {
                input: {
                    query: 'test query',
                    maxRules: 15 // Exceeds maximum of 10
                }
            };
            const mockToken = {};
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            const parts = result.content;
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('Error: maxRules must be between 1 and 10'));
            }
        });
        test('should handle context manager errors gracefully', async () => {
            const mockOptions = {
                input: {
                    query: 'architecture patterns',
                    maxRules: 5
                }
            };
            const mockToken = {};
            mockContextManager.retrieveContext.rejects(new Error('Repository not found'));
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            const parts = result.content;
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('Error retrieving context'));
                assert.ok(firstPart.value.includes('Repository not found'));
            }
        });
        test('should handle no results found', async () => {
            const mockOptions = {
                input: {
                    query: 'nonexistent topic',
                    maxRules: 5
                }
            };
            const mockToken = {};
            const emptyResult = {
                ...mockData_1.mockContextRetrievalResult,
                rules: [],
                totalFound: 0,
                scores: []
            };
            mockContextManager.retrieveContext.resolves(emptyResult);
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            const parts = result.content;
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('No relevant rules found'));
            }
        });
        test('should format output correctly with multiple rules', async () => {
            const multipleRulesResult = {
                ...mockData_1.mockContextRetrievalResult,
                rules: [
                    mockData_1.mockContextRetrievalResult.rules[0],
                    {
                        filename: '01-workflow.mdc',
                        name: 'workflow',
                        category: 1,
                        service: 'backend',
                        path: 'backend/01-workflow.mdc',
                        content: '# Workflow\n\nWorkflow guidelines...',
                        summary: 'Workflow guidelines for development'
                    }
                ],
                totalFound: 2,
                scores: [100, 80]
            };
            const mockOptions = {
                input: {
                    query: 'development guidelines',
                    maxRules: 5,
                    includeContent: true
                }
            };
            const mockToken = {};
            mockContextManager.retrieveContext.resolves(multipleRulesResult);
            const result = await contextRetrievalTool.invoke(mockOptions, mockToken);
            const parts = result.content;
            const firstPart = parts[0];
            if (firstPart instanceof vscode.LanguageModelTextPart) {
                assert.ok(firstPart.value.includes('Found 2 relevant rules'));
                assert.ok(firstPart.value.includes('clean-architecture'));
                assert.ok(firstPart.value.includes('workflow'));
                assert.ok(firstPart.value.includes('Relevance: 100%'));
                assert.ok(firstPart.value.includes('Relevance: 80%'));
            }
        });
    });
    suite('prepareInvocation', () => {
        test('should prepare invocation with confirmation message', async () => {
            const mockOptions = {
                input: {
                    query: 'architecture patterns',
                    maxRules: 5
                }
            };
            const mockToken = {};
            const result = await contextRetrievalTool.prepareInvocation(mockOptions, mockToken);
            assert.ok(result.invocationMessage);
            assert.ok(result.confirmationMessages);
            assert.ok(result.confirmationMessages.title);
            assert.ok(result.confirmationMessages.message);
            assert.ok(result.invocationMessage.includes('Retrieving context'));
            assert.ok(result.confirmationMessages.title.includes('Retrieve Development Rules'));
        });
        test('should include query details in confirmation message', async () => {
            const mockOptions = {
                input: {
                    query: 'specific architecture patterns',
                    maxRules: 3,
                    service: 'backend'
                }
            };
            const mockToken = {};
            const result = await contextRetrievalTool.prepareInvocation(mockOptions, mockToken);
            const messageValue = result.confirmationMessages.message.value;
            assert.ok(messageValue.includes('specific architecture patterns'));
            assert.ok(messageValue.includes('3 rules'));
            assert.ok(messageValue.includes('backend'));
        });
    });
});
//# sourceMappingURL=contextRetrievalTool.test.js.map