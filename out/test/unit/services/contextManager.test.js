"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const sinon = __importStar(require("sinon"));
const contextManager_1 = require("../../../services/contextManager");
const rules_1 = require("../../../types/rules");
const mockData_1 = require("../../fixtures/mockData");
suite('ContextManager Unit Tests', () => {
    let contextManager;
    let mockLogger;
    let mockGithubService;
    let mockStorageService;
    setup(() => {
        mockLogger = {
            debug: sinon.stub(),
            info: sinon.stub(),
            warn: sinon.stub(),
            error: sinon.stub()
        };
        mockGithubService = {
            fetchRepositoryContent: sinon.stub(),
            fetchRepositoryContentRecursive: sinon.stub(),
            fetchFileContent: sinon.stub()
        };
        mockStorageService = {
            getLastRepository: sinon.stub(),
            setLastRepository: sinon.stub(),
            getSelectedFiles: sinon.stub(),
            setSelectedFiles: sinon.stub(),
            clearSelectedFiles: sinon.stub()
        };
        contextManager = new contextManager_1.ContextManager(mockLogger, mockGithubService, mockStorageService);
    });
    teardown(() => {
        sinon.restore();
    });
    suite('retrieveContext', () => {
        test('should retrieve context successfully', async () => {
            // Setup mocks
            mockStorageService.getLastRepository.returns(mockData_1.mockRepository);
            // Mock repository structure retrieval
            sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockData_1.mockRepositoryStructure);
            // Mock rule search
            sinon.stub(contextManager, 'searchRules').resolves(mockData_1.mockRuleContent);
            // Mock rule content retrieval
            sinon.stub(contextManager, 'getRuleContent').resolves(mockData_1.mockRuleContent[0]);
            const result = await contextManager.retrieveContext(mockData_1.mockContextRetrievalParams);
            assert.strictEqual(result.query, mockData_1.mockContextRetrievalParams.query);
            assert.strictEqual(result.totalFound, mockData_1.mockRuleContent.length);
            assert.ok(result.rules.length > 0);
            assert.ok(result.scores.length > 0);
            assert.strictEqual(result.repositoryStructure, mockData_1.mockRepositoryStructure);
        });
        test('should handle no repository selected', async () => {
            mockStorageService.getLastRepository.returns(null);
            try {
                await contextManager.retrieveContext(mockData_1.mockContextRetrievalParams);
                assert.fail('Should have thrown an error');
            }
            catch (error) {
                assert.ok(error instanceof Error);
                assert.ok(error.message.includes('No repository structure available'));
            }
        });
        test('should limit results based on maxRules parameter', async () => {
            mockStorageService.getLastRepository.returns(mockData_1.mockRepository);
            sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockData_1.mockRepositoryStructure);
            sinon.stub(contextManager, 'searchRules').resolves(mockData_1.mockRuleContent);
            sinon.stub(contextManager, 'getRuleContent').resolves(mockData_1.mockRuleContent[0]);
            const params = { ...mockData_1.mockContextRetrievalParams, maxRules: 1 };
            const result = await contextManager.retrieveContext(params);
            assert.strictEqual(result.rules.length, 1);
            assert.strictEqual(result.scores.length, 1);
        });
    });
    suite('getRepositoryStructure', () => {
        test('should return cached repository structure if valid', async () => {
            mockStorageService.getLastRepository.returns(mockData_1.mockRepository);
            // Set up cache with recent timestamp
            const recentStructure = {
                ...mockData_1.mockRepositoryStructure,
                lastParsed: new Date(Date.now() - 1000) // 1 second ago
            };
            contextManager.repositoryStructureCache.set('test-org/test-repo', recentStructure);
            const result = await contextManager.getRepositoryStructure();
            assert.strictEqual(result, recentStructure);
        });
        test('should fetch new structure if cache is expired', async () => {
            mockStorageService.getLastRepository.returns(mockData_1.mockRepository);
            // Set up cache with old timestamp
            const oldStructure = {
                ...mockData_1.mockRepositoryStructure,
                lastParsed: new Date(Date.now() - 4000000) // More than 1 hour ago
            };
            contextManager.repositoryStructureCache.set('test-org/test-repo', oldStructure);
            // Mock repository parser
            const mockRepositoryParser = contextManager.repositoryParser;
            sinon.stub(mockRepositoryParser, 'parseRepositoryStructure').resolves(mockData_1.mockRepositoryStructure);
            const result = await contextManager.getRepositoryStructure();
            assert.notStrictEqual(result, oldStructure);
            assert.strictEqual(result, mockData_1.mockRepositoryStructure);
        });
    });
    suite('searchRules', () => {
        test('should search rules across all services', async () => {
            sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockData_1.mockRepositoryStructure);
            mockStorageService.getLastRepository.returns(mockData_1.mockRepository);
            const mockRepositoryParser = contextManager.repositoryParser;
            sinon.stub(mockRepositoryParser, 'discoverRuleFiles').resolves([
                {
                    filename: '00-clean-architecture.mdc',
                    name: 'clean-architecture',
                    category: rules_1.RuleCategory.ARCHITECTURE,
                    service: 'backend',
                    path: 'backend/00-clean-architecture.mdc'
                }
            ]);
            sinon.stub(contextManager, 'getRuleContent').resolves(mockData_1.mockRuleContent[0]);
            const criteria = {
                keywords: ['architecture'],
                searchContent: true
            };
            const result = await contextManager.searchRules(criteria);
            assert.ok(result.length > 0);
            assert.strictEqual(result[0].name, 'clean-architecture');
        });
        test('should filter by service if specified', async () => {
            sinon.stub(contextManager, 'getRepositoryStructure').resolves(mockData_1.mockRepositoryStructure);
            mockStorageService.getLastRepository.returns(mockData_1.mockRepository);
            const mockRepositoryParser = contextManager.repositoryParser;
            sinon.stub(mockRepositoryParser, 'discoverRuleFiles').resolves([]);
            const criteria = {
                keywords: ['test'],
                service: 'backend',
                searchContent: true
            };
            await contextManager.searchRules(criteria);
            // Verify that discoverRuleFiles was called only for backend service
            assert.ok(mockRepositoryParser.discoverRuleFiles.calledWith(mockData_1.mockRepository.owner, mockData_1.mockRepository.name, 'backend'));
        });
    });
    suite('getRuleContent', () => {
        test('should return cached rule content if available', async () => {
            const metadata = mockData_1.mockRuleContent[0];
            const cacheKey = `${metadata.service}/${metadata.filename}`;
            // Set up cache
            contextManager.ruleContentCache.set(cacheKey, metadata);
            const result = await contextManager.getRuleContent(metadata);
            assert.strictEqual(result, metadata);
        });
        test('should fetch and cache rule content if not cached', async () => {
            const metadata = {
                filename: '00-clean-architecture.mdc',
                name: 'clean-architecture',
                category: rules_1.RuleCategory.ARCHITECTURE,
                service: 'backend',
                path: 'backend/00-clean-architecture.mdc'
            };
            mockStorageService.getLastRepository.returns(mockData_1.mockRepository);
            // Mock GitHub API calls
            mockGithubService.fetchRepositoryContent.resolves({
                success: true,
                data: [{
                        name: '00-clean-architecture.mdc',
                        path: 'backend/00-clean-architecture.mdc',
                        download_url: 'https://example.com/download',
                        type: 'file',
                        sha: 'abc123',
                        size: 1024,
                        url: 'https://example.com/api',
                        html_url: 'https://example.com/html',
                        git_url: 'https://example.com/git'
                    }]
            });
            mockGithubService.fetchFileContent.resolves({
                success: true,
                data: mockData_1.mockFileContentResponses['backend/00-clean-architecture.mdc']
            });
            // Mock rule parser
            const mockRuleParser = contextManager.ruleParser;
            sinon.stub(mockRuleParser, 'parseRuleContent').returns(mockData_1.mockRuleContent[0]);
            const result = await contextManager.getRuleContent(metadata);
            assert.strictEqual(result.filename, metadata.filename);
            assert.strictEqual(result.name, metadata.name);
        });
    });
    suite('clearCache', () => {
        test('should clear all caches', () => {
            // Set up some cache data
            contextManager.repositoryStructureCache.set('test', {});
            contextManager.ruleContentCache.set('test', {});
            contextManager.clearCache();
            assert.strictEqual(contextManager.repositoryStructureCache.size, 0);
            assert.strictEqual(contextManager.ruleContentCache.size, 0);
        });
    });
    suite('session management', () => {
        test('should initialize new session', () => {
            const sessionId = contextManager.initializeSession();
            assert.ok(sessionId);
            assert.ok(sessionId.startsWith('session_'));
            const session = contextManager.getSession(sessionId);
            assert.ok(session);
            assert.strictEqual(session.id, sessionId);
            assert.ok(session.startTime instanceof Date);
            assert.ok(session.lastActivity instanceof Date);
        });
        test('should return null for non-existent session', () => {
            const session = contextManager.getSession('non-existent');
            assert.strictEqual(session, null);
        });
    });
});
//# sourceMappingURL=contextManager.test.js.map