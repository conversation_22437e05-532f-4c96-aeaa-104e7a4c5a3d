"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const sinon = __importStar(require("sinon"));
const ruleParser_1 = require("../../../services/ruleParser");
const rules_1 = require("../../../types/rules");
suite('RuleParser Unit Tests', () => {
    let ruleParser;
    let mockLogger;
    setup(() => {
        mockLogger = {
            debug: sinon.stub(),
            info: sinon.stub(),
            warn: sinon.stub(),
            error: sinon.stub()
        };
        ruleParser = new ruleParser_1.RuleParser(mockLogger);
    });
    teardown(() => {
        sinon.restore();
    });
    suite('parseRuleFilename', () => {
        test('should parse valid rule filename without version or specificity', () => {
            const result = ruleParser.parseRuleFilename('00-clean-architecture.mdc', 'backend', 'backend/00-clean-architecture.mdc');
            assert.strictEqual(result?.filename, '00-clean-architecture.mdc');
            assert.strictEqual(result?.name, 'clean-architecture');
            assert.strictEqual(result?.category, rules_1.RuleCategory.ARCHITECTURE);
            assert.strictEqual(result?.service, 'backend');
            assert.strictEqual(result?.path, 'backend/00-clean-architecture.mdc');
            assert.strictEqual(result?.version, undefined);
            assert.strictEqual(result?.specificity, undefined);
        });
        test('should parse valid rule filename with version', () => {
            const result = ruleParser.parseRuleFilename('<EMAIL>', 'backend', 'backend/<EMAIL>');
            assert.strictEqual(result?.filename, '<EMAIL>');
            assert.strictEqual(result?.name, 'pr-workflow');
            assert.strictEqual(result?.category, rules_1.RuleCategory.WORKFLOWS);
            assert.strictEqual(result?.service, 'backend');
            assert.strictEqual(result?.path, 'backend/<EMAIL>');
            assert.strictEqual(result?.version, '1.2.0');
            assert.strictEqual(result?.specificity, undefined);
        });
        test('should parse valid rule filename with specificity', () => {
            const result = ruleParser.parseRuleFilename('02-rest-api-guidelines-frontend.mdc', 'frontend', 'frontend/02-rest-api-guidelines-frontend.mdc');
            assert.strictEqual(result?.filename, '02-rest-api-guidelines-frontend.mdc');
            assert.strictEqual(result?.name, 'rest-api-guidelines');
            assert.strictEqual(result?.category, rules_1.RuleCategory.OPENAPI);
            assert.strictEqual(result?.service, 'frontend');
            assert.strictEqual(result?.path, 'frontend/02-rest-api-guidelines-frontend.mdc');
            assert.strictEqual(result?.version, undefined);
            assert.strictEqual(result?.specificity, 'frontend');
        });
        test('should parse valid rule filename with both version and specificity', () => {
            const result = ruleParser.parseRuleFilename('<EMAIL>', 'backend', 'backend/<EMAIL>');
            assert.strictEqual(result?.filename, '<EMAIL>');
            assert.strictEqual(result?.name, 'domain-rules');
            assert.strictEqual(result?.category, rules_1.RuleCategory.DOMAIN_RULES);
            assert.strictEqual(result?.service, 'backend');
            assert.strictEqual(result?.path, 'backend/<EMAIL>');
            assert.strictEqual(result?.version, '2.1.0');
            assert.strictEqual(result?.specificity, 'backend');
        });
        test('should return null for invalid filename format', () => {
            const result = ruleParser.parseRuleFilename('invalid-filename.mdc', 'backend', 'backend/invalid-filename.mdc');
            assert.strictEqual(result, null);
        });
        test('should return null for non-mdc files', () => {
            const result = ruleParser.parseRuleFilename('00-clean-architecture.txt', 'backend', 'backend/00-clean-architecture.txt');
            assert.strictEqual(result, null);
        });
        test('should return null for invalid category number', () => {
            const result = ruleParser.parseRuleFilename('99-invalid-category.mdc', 'backend', 'backend/99-invalid-category.mdc');
            assert.strictEqual(result, null);
        });
    });
    suite('parseRuleContent', () => {
        test('should parse rule content and extract summary', () => {
            const metadata = {
                filename: '00-clean-architecture.mdc',
                name: 'clean-architecture',
                category: rules_1.RuleCategory.ARCHITECTURE,
                service: 'backend',
                path: 'backend/00-clean-architecture.mdc'
            };
            const content = `# Clean Architecture

This is a comprehensive guide to clean architecture principles.

## Overview

Clean architecture promotes separation of concerns...

## Implementation

Follow these steps to implement clean architecture...`;
            const result = ruleParser.parseRuleContent(metadata, content);
            assert.strictEqual(result.filename, metadata.filename);
            assert.strictEqual(result.name, metadata.name);
            assert.strictEqual(result.category, metadata.category);
            assert.strictEqual(result.service, metadata.service);
            assert.strictEqual(result.path, metadata.path);
            assert.strictEqual(result.content, content);
            assert.strictEqual(result.summary, 'This is a comprehensive guide to clean architecture principles.');
        });
        test('should handle content without clear summary', () => {
            const metadata = {
                filename: '01-workflow.mdc',
                name: 'workflow',
                category: rules_1.RuleCategory.WORKFLOWS,
                service: 'backend',
                path: 'backend/01-workflow.mdc'
            };
            const content = `# Workflow

## Step 1
Do this...

## Step 2
Do that...`;
            const result = ruleParser.parseRuleContent(metadata, content);
            assert.strictEqual(result.summary, 'No summary available');
        });
    });
    suite('extractSummaryFromContent', () => {
        test('should extract first paragraph as summary', () => {
            const content = `# Title

This is the first paragraph that should be extracted as summary.

This is the second paragraph that should not be included.`;
            const summary = ruleParser.extractSummaryFromContent(content);
            assert.strictEqual(summary, 'This is the first paragraph that should be extracted as summary.');
        });
        test('should handle content with no paragraphs', () => {
            const content = `# Title

## Section 1
Content here...`;
            const summary = ruleParser.extractSummaryFromContent(content);
            assert.strictEqual(summary, 'No summary available');
        });
        test('should truncate long summaries', () => {
            const longText = 'This is a very long summary that exceeds the maximum length limit and should be truncated to ensure it fits within the expected bounds for summary text in the rule parser functionality.';
            const content = `# Title

${longText}

More content...`;
            const summary = ruleParser.extractSummaryFromContent(content);
            assert.ok(summary.length <= 200);
            assert.ok(summary.endsWith('...'));
        });
        test('should handle empty content', () => {
            const summary = ruleParser.extractSummaryFromContent('');
            assert.strictEqual(summary, 'No summary available');
        });
    });
});
//# sourceMappingURL=ruleParser.test.js.map