"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitHubApiResponseHandler = void 0;
var GitHubApiResponseHandler = /** @class */ (function () {
    function GitHubApiResponseHandler(rateLimitManager, logger) {
        this.rateLimitManager = rateLimitManager;
        this.logger = logger;
    }
    GitHubApiResponseHandler.prototype.handleResponse = function (response, isRawContent) {
        if (isRawContent === void 0) { isRawContent = false; }
        this.rateLimitManager.updateFromHeaders(response.headers);
        if (response.statusCode === 200) {
            try {
                if (isRawContent) {
                    if (typeof response.body === "string") {
                        return { success: true, data: response.body };
                    }
                    this.logger.error("Raw content response body is not a string.");
                    return {
                        success: false,
                        error: new Error("Invalid raw content response format"),
                    };
                }
                var parsedData = JSON.parse(response.body);
                return { success: true, data: parsedData };
            }
            catch (error) {
                this.logger.error("Failed to parse GitHub API response JSON", error);
                return {
                    success: false,
                    error: new Error("Failed to parse response JSON: ".concat(error instanceof Error ? error.message : String(error))),
                };
            }
        }
        else if (response.statusCode === 403 &&
            this.rateLimitManager.isLimitExceeded()) {
            var resetTime = this.rateLimitManager.getResetTime();
            var errorMessage = "GitHub API rate limit exceeded.".concat(resetTime ? " Reset at ".concat(resetTime.toLocaleTimeString()) : "");
            this.logger.warn(errorMessage);
            return { success: false, error: new Error(errorMessage) };
        }
        else {
            var errorData = {
                message: "GitHub API Error: ".concat(response.statusCode),
            };
            try {
                var parsedError = JSON.parse(response.body);
                if (parsedError && typeof parsedError.message === "string") {
                    errorData = parsedError;
                }
            }
            catch (e) {
                this.logger.debug("Could not parse error response body: ".concat(e instanceof Error ? e.message : String(e)));
                errorData.message = "".concat(errorData.message, " - ").concat(response.body);
            }
            errorData.status = response.statusCode;
            this.logger.error("GitHub API Error: ".concat(errorData.status, " - ").concat(errorData.message));
            return { success: false, error: errorData };
        }
    };
    return GitHubApiResponseHandler;
}());
exports.GitHubApiResponseHandler = GitHubApiResponseHandler;
