"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var assert = require("assert");
var vscode = require("vscode");
var sinon = require("sinon");
suite('Extension Integration Tests', function () {
    var sandbox;
    setup(function () {
        sandbox = sinon.createSandbox();
    });
    teardown(function () {
        sandbox.restore();
    });
    test('Extension should be present and activate', function () { return __awaiter(void 0, void 0, void 0, function () {
        var extension;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    extension = vscode.extensions.getExtension('undefined_publisher.xendit-copilot');
                    assert.ok(extension, 'Extension should be present');
                    if (!!extension.isActive) return [3 /*break*/, 2];
                    return [4 /*yield*/, extension.activate()];
                case 1:
                    _a.sent();
                    _a.label = 2;
                case 2:
                    assert.ok(extension.isActive, 'Extension should be active');
                    return [2 /*return*/];
            }
        });
    }); });
    test('Chat tools should be registered', function () { return __awaiter(void 0, void 0, void 0, function () {
        var tools, xenditTool;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: 
                // Wait for extension to fully activate
                return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 1000); })];
                case 1:
                    // Wait for extension to fully activate
                    _a.sent();
                    tools = vscode.lm.tools;
                    xenditTool = tools.find(function (tool) { return tool.name === 'xendit-copilot_contextRetrieval'; });
                    assert.ok(xenditTool, 'xendit-copilot_contextRetrieval tool should be registered');
                    return [2 /*return*/];
            }
        });
    }); });
    test('Commands should be registered', function () { return __awaiter(void 0, void 0, void 0, function () {
        var commands, expectedCommands, _i, expectedCommands_1, command;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, vscode.commands.getCommands()];
                case 1:
                    commands = _a.sent();
                    expectedCommands = [
                        'aidd.setRepository',
                        'aidd.refresh',
                        'aidd.toggleSelection',
                        'aidd.downloadSelected',
                        'aidd.welcome',
                        'aidd.openSettings',
                        'aidd.clearStorage',
                        'aidd.refreshRuleStatus'
                    ];
                    for (_i = 0, expectedCommands_1 = expectedCommands; _i < expectedCommands_1.length; _i++) {
                        command = expectedCommands_1[_i];
                        assert.ok(commands.includes(command), "Command ".concat(command, " should be registered"));
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    test('Views should be registered', function () { return __awaiter(void 0, void 0, void 0, function () {
        var welcomeView;
        return __generator(this, function (_a) {
            welcomeView = vscode.window.createWebviewPanel('test', 'Test', vscode.ViewColumn.One, {});
            assert.ok(welcomeView, 'Should be able to create webview panels');
            welcomeView.dispose();
            return [2 /*return*/];
        });
    }); });
    test('Configuration should be available', function () {
        var config = vscode.workspace.getConfiguration('aidd');
        // Test that configuration properties exist
        var githubToken = config.get('githubToken');
        var maxRecentRepositories = config.get('maxRecentRepositories');
        var showWelcomeOnStartup = config.get('showWelcomeOnStartup');
        assert.strictEqual(typeof githubToken, 'string');
        assert.strictEqual(typeof maxRecentRepositories, 'number');
        assert.strictEqual(typeof showWelcomeOnStartup, 'boolean');
    });
    test('Extension should handle activation events', function () { return __awaiter(void 0, void 0, void 0, function () {
        var extension;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    extension = vscode.extensions.getExtension('undefined_publisher.xendit-copilot');
                    assert.ok(extension);
                    if (!!extension.isActive) return [3 /*break*/, 2];
                    return [4 /*yield*/, extension.activate()];
                case 1:
                    _a.sent();
                    _a.label = 2;
                case 2:
                    // Extension should be active without errors
                    assert.ok(extension.isActive);
                    return [2 /*return*/];
            }
        });
    }); });
});
