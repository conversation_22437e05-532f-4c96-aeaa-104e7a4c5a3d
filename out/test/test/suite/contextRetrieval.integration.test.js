"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var assert = require("assert");
var vscode = require("vscode");
var nock = require("nock");
var contextManager_1 = require("../../services/contextManager");
var contextRetrievalTool_1 = require("../../tools/contextRetrievalTool");
var logger_1 = require("../../services/logger");
var storage_1 = require("../../services/storage");
var github_1 = require("../../api/github");
var httpClient_1 = require("../../services/httpClient");
var rateLimitManager_1 = require("../../services/rateLimitManager");
var mockData_1 = require("../fixtures/mockData");
suite('Context Retrieval Integration Tests', function () {
    var contextManager;
    var contextRetrievalTool;
    var logger;
    var storageService;
    var githubService;
    setup(function () { return __awaiter(void 0, void 0, void 0, function () {
        var mockContext, httpClient, rateLimitManager;
        return __generator(this, function (_a) {
            // Create real services for integration testing
            logger = new logger_1.Logger('Test', false);
            mockContext = {
                globalState: {
                    get: function () { return undefined; },
                    update: function () { return Promise.resolve(); },
                    keys: function () { return []; }
                },
                workspaceState: {
                    get: function () { return undefined; },
                    update: function () { return Promise.resolve(); },
                    keys: function () { return []; }
                }
            };
            storageService = new storage_1.StorageService(mockContext);
            httpClient = new httpClient_1.HttpClient(logger);
            rateLimitManager = new rateLimitManager_1.RateLimitManager(logger);
            githubService = new github_1.GitHubApiService(httpClient, rateLimitManager, logger);
            contextManager = new contextManager_1.ContextManager(logger, githubService, storageService);
            contextRetrievalTool = new contextRetrievalTool_1.ContextRetrievalTool(logger, contextManager);
            // Set up the test repository
            storageService.setLastRepository(mockData_1.mockRepository);
            return [2 /*return*/];
        });
    }); });
    teardown(function () {
        nock.cleanAll();
    });
    test('End-to-end context retrieval with mocked GitHub API', function () { return __awaiter(void 0, void 0, void 0, function () {
        var baseUrl, _i, _a, _b, path, content, mockOptions, mockToken, result, parts, firstPart;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    baseUrl = 'https://api.github.com';
                    // Mock main.mdc fetch
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/main.mdc')
                        .reply(200, [{
                            name: 'main.mdc',
                            path: 'main.mdc',
                            download_url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/main.mdc?ref=main"),
                            type: 'file',
                            sha: 'main123',
                            size: 1024,
                            url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/main.mdc"),
                            html_url: 'https://github.com/test-org/test-repo/blob/main/main.mdc',
                            git_url: "".concat(baseUrl, "/repos/test-org/test-repo/git/blobs/main123")
                        }]);
                    // Mock main.mdc content
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/main.mdc?ref=main')
                        .reply(200, mockData_1.mockMainMdcContent);
                    // Mock root directory listing for auto-discovery
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/')
                        .reply(200, [
                        {
                            name: 'backend',
                            path: 'backend',
                            type: 'dir',
                            sha: 'backend123',
                            size: 0,
                            url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/backend"),
                            html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
                            git_url: "".concat(baseUrl, "/repos/test-org/test-repo/git/trees/backend123")
                        },
                        {
                            name: 'frontend',
                            path: 'frontend',
                            type: 'dir',
                            sha: 'frontend123',
                            size: 0,
                            url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/frontend"),
                            html_url: 'https://github.com/test-org/test-repo/tree/main/frontend',
                            git_url: "".concat(baseUrl, "/repos/test-org/test-repo/git/trees/frontend123")
                        }
                    ]);
                    // Mock service directory listings
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/backend')
                        .reply(200, mockData_1.mockGithubContent.filter(function (item) { return item.path.startsWith('backend/'); }));
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/frontend')
                        .reply(200, mockData_1.mockGithubContent.filter(function (item) { return item.path.startsWith('frontend/'); }));
                    // Mock file content downloads
                    for (_i = 0, _a = Object.entries(mockData_1.mockFileContentResponses); _i < _a.length; _i++) {
                        _b = _a[_i], path = _b[0], content = _b[1];
                        if (path !== 'main.mdc') {
                            nock(baseUrl)
                                .get("/repos/test-org/test-repo/contents/".concat(path))
                                .reply(200, [{
                                    name: path.split('/').pop(),
                                    path: path,
                                    download_url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/").concat(path, "?ref=main"),
                                    type: 'file',
                                    sha: 'file123',
                                    size: content.length,
                                    url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/").concat(path),
                                    html_url: "https://github.com/test-org/test-repo/blob/main/".concat(path),
                                    git_url: "".concat(baseUrl, "/repos/test-org/test-repo/git/blobs/file123")
                                }]);
                            nock(baseUrl)
                                .get("/repos/test-org/test-repo/contents/".concat(path, "?ref=main"))
                                .reply(200, content);
                        }
                    }
                    mockOptions = {
                        input: {
                            query: 'architecture patterns',
                            maxRules: 5,
                            includeContent: true
                        }
                    };
                    mockToken = {};
                    return [4 /*yield*/, contextRetrievalTool.invoke(mockOptions, mockToken)];
                case 1:
                    result = _c.sent();
                    // Verify the result
                    assert.ok(result instanceof vscode.LanguageModelToolResult);
                    parts = result.content;
                    assert.ok(parts.length > 0);
                    firstPart = parts[0];
                    if (firstPart instanceof vscode.LanguageModelTextPart) {
                        // Should find at least one relevant rule
                        assert.ok(firstPart.value.includes('relevant rules') || firstPart.value.includes('No relevant rules'));
                        // If rules were found, verify the format
                        if (firstPart.value.includes('relevant rules')) {
                            assert.ok(firstPart.value.includes('**Rule'));
                            assert.ok(firstPart.value.includes('Category:'));
                            assert.ok(firstPart.value.includes('Service:'));
                        }
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    test('Context retrieval with no repository selected', function () { return __awaiter(void 0, void 0, void 0, function () {
        var mockOptions, mockToken, result, parts, firstPart;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // Clear the repository
                    storageService.setLastRepository(null);
                    mockOptions = {
                        input: {
                            query: 'architecture patterns',
                            maxRules: 5
                        }
                    };
                    mockToken = {};
                    return [4 /*yield*/, contextRetrievalTool.invoke(mockOptions, mockToken)];
                case 1:
                    result = _a.sent();
                    parts = result.content;
                    firstPart = parts[0];
                    if (firstPart instanceof vscode.LanguageModelTextPart) {
                        assert.ok(firstPart.value.includes('Error') || firstPart.value.includes('No repository'));
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    test('Context retrieval with GitHub API errors', function () { return __awaiter(void 0, void 0, void 0, function () {
        var mockOptions, mockToken, result, parts, firstPart;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // Set up repository
                    storageService.setLastRepository(mockData_1.mockRepository);
                    // Mock GitHub API to return errors
                    nock('https://api.github.com')
                        .get('/repos/test-org/test-repo/contents/main.mdc')
                        .reply(404, { message: 'Not Found' });
                    nock('https://api.github.com')
                        .get('/repos/test-org/test-repo/contents/')
                        .reply(403, { message: 'API rate limit exceeded' });
                    mockOptions = {
                        input: {
                            query: 'architecture patterns',
                            maxRules: 5
                        }
                    };
                    mockToken = {};
                    return [4 /*yield*/, contextRetrievalTool.invoke(mockOptions, mockToken)];
                case 1:
                    result = _a.sent();
                    parts = result.content;
                    firstPart = parts[0];
                    if (firstPart instanceof vscode.LanguageModelTextPart) {
                        // Should handle the error gracefully
                        assert.ok(firstPart.value.includes('Error') || firstPart.value.includes('No relevant rules'));
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    test('Context manager caching behavior', function () { return __awaiter(void 0, void 0, void 0, function () {
        var baseUrl, structure1, structure2, structure3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    // Set up repository
                    storageService.setLastRepository(mockData_1.mockRepository);
                    baseUrl = 'https://api.github.com';
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/main.mdc')
                        .reply(404); // main.mdc not found, should trigger auto-discovery
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/')
                        .reply(200, [
                        {
                            name: 'backend',
                            path: 'backend',
                            type: 'dir',
                            sha: 'backend123',
                            size: 0,
                            url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/backend"),
                            html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
                            git_url: "".concat(baseUrl, "/repos/test-org/test-repo/git/trees/backend123")
                        }
                    ]);
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/backend')
                        .reply(200, []);
                    return [4 /*yield*/, contextManager.getRepositoryStructure()];
                case 1:
                    structure1 = _a.sent();
                    assert.ok(structure1);
                    return [4 /*yield*/, contextManager.getRepositoryStructure()];
                case 2:
                    structure2 = _a.sent();
                    assert.strictEqual(structure1, structure2);
                    // Clear cache and verify it refetches
                    contextManager.clearCache();
                    // Set up the same mock again for the refetch
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/')
                        .reply(200, [
                        {
                            name: 'backend',
                            path: 'backend',
                            type: 'dir',
                            sha: 'backend123',
                            size: 0,
                            url: "".concat(baseUrl, "/repos/test-org/test-repo/contents/backend"),
                            html_url: 'https://github.com/test-org/test-repo/tree/main/backend',
                            git_url: "".concat(baseUrl, "/repos/test-org/test-repo/git/trees/backend123")
                        }
                    ]);
                    nock(baseUrl)
                        .get('/repos/test-org/test-repo/contents/backend')
                        .reply(200, []);
                    return [4 /*yield*/, contextManager.getRepositoryStructure()];
                case 3:
                    structure3 = _a.sent();
                    assert.ok(structure3);
                    assert.notStrictEqual(structure1, structure3); // Should be a new instance
                    return [2 /*return*/];
            }
        });
    }); });
});
