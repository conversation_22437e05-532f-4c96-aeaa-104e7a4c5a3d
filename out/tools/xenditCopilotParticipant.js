"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerXenditCopilotChatParticipant = registerXenditCopilotChatParticipant;
const vscode = __importStar(require("vscode"));
/**
 * Register the xendit-copilot chat participant
 */
function registerXenditCopilotChatParticipant(context, logger, contextManager) {
    const handler = async (request, chatContext, stream, token) => {
        logger.info(`Xendit Copilot chat participant invoked with command: ${request.command}`);
        if (request.command === "list") {
            stream.markdown(`Available tools: ${vscode.lm.tools
                .map((tool) => tool.name)
                .join(", ")}\n\n`);
            return;
        }
        // Filter tools to include xendit-copilot tools and general tools
        const tools = vscode.lm.tools.filter((tool) => tool.tags.includes("xendit-copilot") ||
            tool.tags.includes("chat-tools-sample"));
        logger.info(`Found ${tools.length} available tools: ${tools.map(t => t.name).join(", ")}`);
        // Get the best available model
        let model = request.model;
        if (model.vendor === "copilot" && model.family.startsWith("o1")) {
            // The o1 models do not currently support tools
            const models = await vscode.lm.selectChatModels({
                vendor: "copilot",
                family: "gpt-4o",
            });
            model = models[0];
        }
        const options = {
            justification: "To provide development context and assistance",
            tools: tools,
        };
        // Create the system prompt for xendit-copilot
        const systemPrompt = `You are Xendit Copilot, an AI assistant specialized in helping developers with Xendit's development practices and rules.

You have access to tools that can retrieve relevant development rules and context from GitHub repositories. Use these tools when:
- Users ask about architecture patterns, coding standards, or best practices
- Users need guidance on API design, workflows, or domain-specific rules
- Users want to understand existing development conventions

Always be helpful, accurate, and provide actionable guidance based on the retrieved context.`;
        const messages = [
            vscode.LanguageModelChatMessage.User(systemPrompt),
            vscode.LanguageModelChatMessage.User(request.prompt)
        ];
        try {
            // Send the request to the language model
            const response = await model.sendRequest(messages, options, token);
            // Stream the response
            for await (const part of response.stream) {
                if (part instanceof vscode.LanguageModelTextPart) {
                    stream.markdown(part.value);
                }
                else if (part instanceof vscode.LanguageModelToolCallPart) {
                    // Tool calls are handled automatically by the language model
                    logger.info(`Tool called: ${part.name}`);
                }
            }
        }
        catch (error) {
            logger.error("Error in xendit-copilot chat participant:", error);
            stream.markdown("❌ Sorry, I encountered an error while processing your request. Please try again.");
        }
    };
    const participant = vscode.chat.createChatParticipant("chat.xendit-copilot", handler);
    participant.iconPath = new vscode.ThemeIcon("github");
    participant.followupProvider = {
        provideFollowups(result, context, token) {
            return [
                {
                    prompt: "Can you help me with architecture patterns?",
                    label: "🏗️ Architecture Patterns",
                    command: "architecture"
                },
                {
                    prompt: "Show me API design guidelines",
                    label: "🔌 API Guidelines",
                    command: "api"
                },
                {
                    prompt: "What are the workflow best practices?",
                    label: "⚡ Workflow Practices",
                    command: "workflow"
                }
            ];
        }
    };
    context.subscriptions.push(participant);
    logger.info("Xendit Copilot chat participant registered successfully");
}
//# sourceMappingURL=xenditCopilotParticipant.js.map